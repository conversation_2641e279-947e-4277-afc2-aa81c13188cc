using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;
using DatVeXe.Services;
using System.Text.Json;

namespace DatVeXe.Controllers
{
    public class MyTicketsController : Controller
    {
        private readonly DatVeXeContext _context;
        private readonly IEmailService _emailService;
        private readonly ISMSService _smsService;
        private readonly ILogger<MyTicketsController> _logger;

        public MyTicketsController(
            DatVeXeContext context,
            IEmailService emailService,
            ISMSService smsService,
            ILogger<MyTicketsController> logger)
        {
            _context = context;
            _emailService = emailService;
            _smsService = smsService;
            _logger = logger;
        }

        // GET: MyTickets - Danh sách vé của tôi
        public async Task<IActionResult> Index()
        {
            int? userId = HttpContext.Session.GetInt32("UserId");
            if (!userId.HasValue)
            {
                TempData["Error"] = "Vui lòng đăng nhập để xem vé của bạn";
                return RedirectToAction("DangNhap", "TaiKhoan");
            }

            var allTickets = await _context.Ves
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.Xe)
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.TuyenDuong)
                .Include(v => v.ChoNgoi)
                .Include(v => v.KhuyenMai)
                .Where(v => v.NguoiDungId == userId.Value)
                .OrderByDescending(v => v.NgayDat)
                .ToListAsync();

            var now = DateTime.Now;

            var viewModel = new MyTicketsViewModel
            {
                VeDaDat = allTickets.Where(v => v.TrangThai == TrangThaiVe.DaDat && v.ChuyenXe.NgayKhoiHanh > now).ToList(),
                VeSapToi = allTickets.Where(v => v.TrangThai == TrangThaiVe.DaDat && 
                                                v.ChuyenXe.NgayKhoiHanh > now && 
                                                v.ChuyenXe.NgayKhoiHanh <= now.AddDays(7)).ToList(),
                VeDaHoanThanh = allTickets.Where(v => v.TrangThai == TrangThaiVe.DaDat && v.ChuyenXe.NgayKhoiHanh <= now).ToList(),
                VeDaHuy = allTickets.Where(v => v.TrangThai == TrangThaiVe.DaHuy).ToList(),
                TongSoVe = allTickets.Count,
                SoVeSapToi = allTickets.Count(v => v.TrangThai == TrangThaiVe.DaDat && 
                                                  v.ChuyenXe.NgayKhoiHanh > now && 
                                                  v.ChuyenXe.NgayKhoiHanh <= now.AddDays(7)),
                TongChiTieu = allTickets.Where(v => v.TrangThai == TrangThaiVe.DaDat).Sum(v => v.GiaVe - v.SoTienGiam)
            };

            return View(viewModel);
        }

        // GET: MyTickets/Details/{id} - Chi tiết vé
        public async Task<IActionResult> Details(int id)
        {
            int? userId = HttpContext.Session.GetInt32("UserId");
            if (!userId.HasValue)
            {
                TempData["Error"] = "Vui lòng đăng nhập để xem vé của bạn";
                return RedirectToAction("DangNhap", "TaiKhoan");
            }

            var ve = await _context.Ves
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.Xe)
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.TuyenDuong)
                .Include(v => v.ChoNgoi)
                .Include(v => v.KhuyenMai)
                .Include(v => v.DanhGias)
                .FirstOrDefaultAsync(v => v.VeId == id && v.NguoiDungId == userId.Value);

            if (ve == null)
            {
                TempData["Error"] = "Không tìm thấy vé";
                return RedirectToAction("Index");
            }

            var thanhToan = await _context.ThanhToans
                .FirstOrDefaultAsync(t => t.VeId == ve.VeId);

            var now = DateTime.Now;
            var canCancel = ve.TrangThai == TrangThaiVe.DaDat && 
                           ve.ChuyenXe.NgayKhoiHanh > now.AddHours(2);
            var canModify = ve.TrangThai == TrangThaiVe.DaDat && 
                           ve.ChuyenXe.NgayKhoiHanh > now.AddHours(24);
            var canRate = ve.TrangThai == TrangThaiVe.DaDat && 
                         ve.ChuyenXe.NgayKhoiHanh <= now &&
                         !ve.DanhGias.Any();

            var viewModel = new TicketDetailViewModel
            {
                Ve = ve,
                ChuyenXe = ve.ChuyenXe,
                ChoNgoi = ve.ChoNgoi,
                ThanhToan = thanhToan,
                QRCodeData = GenerateQRCode(ve),
                CanCancel = canCancel,
                CanModify = canModify,
                CanRate = canRate,
                DanhGia = ve.DanhGias?.FirstOrDefault()
            };

            return View(viewModel);
        }

        // GET: MyTickets/Cancel/{id} - Hủy vé
        public async Task<IActionResult> Cancel(int id)
        {
            int? userId = HttpContext.Session.GetInt32("UserId");
            if (!userId.HasValue)
            {
                TempData["Error"] = "Vui lòng đăng nhập";
                return RedirectToAction("DangNhap", "TaiKhoan");
            }

            var ve = await _context.Ves
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.Xe)
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.TuyenDuong)
                .FirstOrDefaultAsync(v => v.VeId == id && v.NguoiDungId == userId.Value);

            if (ve == null)
            {
                TempData["Error"] = "Không tìm thấy vé";
                return RedirectToAction("Index");
            }

            if (ve.TrangThai != TrangThaiVe.DaDat)
            {
                TempData["Error"] = "Vé này không thể hủy";
                return RedirectToAction("Details", new { id });
            }

            var now = DateTime.Now;
            if (ve.ChuyenXe.NgayKhoiHanh <= now.AddHours(2))
            {
                TempData["Error"] = "Không thể hủy vé trong vòng 2 giờ trước giờ khởi hành";
                return RedirectToAction("Details", new { id });
            }

            // Tính phí hủy
            var hoursBeforeDeparture = (ve.ChuyenXe.NgayKhoiHanh - now).TotalHours;
            decimal phiHuyPercent = 0;
            
            if (hoursBeforeDeparture < 24)
                phiHuyPercent = 0.2m; // 20%
            else if (hoursBeforeDeparture < 48)
                phiHuyPercent = 0.1m; // 10%
            else
                phiHuyPercent = 0.05m; // 5%

            var phiHuy = (ve.GiaVe - ve.SoTienGiam) * phiHuyPercent;
            var soTienHoanLai = (ve.GiaVe - ve.SoTienGiam) - phiHuy;

            var viewModel = new CancelTicketViewModel
            {
                VeId = ve.VeId,
                MaVe = ve.MaVe,
                ChuyenXe = ve.ChuyenXe,
                PhiHuy = phiHuy,
                SoTienHoanLai = soTienHoanLai
            };

            return View(viewModel);
        }

        // POST: MyTickets/Cancel/{id} - Xử lý hủy vé
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Cancel(int id, CancelTicketViewModel model)
        {
            int? userId = HttpContext.Session.GetInt32("UserId");
            if (!userId.HasValue)
            {
                TempData["Error"] = "Vui lòng đăng nhập";
                return RedirectToAction("DangNhap", "TaiKhoan");
            }

            if (!ModelState.IsValid)
            {
                var ve = await _context.Ves
                    .Include(v => v.ChuyenXe)
                    .ThenInclude(c => c.Xe)
                    .Include(v => v.ChuyenXe)
                    .ThenInclude(c => c.TuyenDuong)
                    .FirstOrDefaultAsync(v => v.VeId == id && v.NguoiDungId == userId.Value);

                if (ve != null)
                {
                    model.ChuyenXe = ve.ChuyenXe;
                    model.MaVe = ve.MaVe;
                }
                return View(model);
            }

            if (!model.XacNhanHuy)
            {
                ModelState.AddModelError("XacNhanHuy", "Vui lòng xác nhận hủy vé");
                return View(model);
            }

            try
            {
                var ve = await _context.Ves
                    .Include(v => v.ChuyenXe)
                    .Include(v => v.NguoiDung)
                    .FirstOrDefaultAsync(v => v.VeId == id && v.NguoiDungId == userId.Value);

                if (ve == null)
                {
                    TempData["Error"] = "Không tìm thấy vé";
                    return RedirectToAction("Index");
                }

                // Cập nhật trạng thái vé
                ve.TrangThai = TrangThaiVe.DaHuy;
                ve.NgayHuy = DateTime.Now;
                ve.LyDoHuy = model.LyDoHuy;

                await _context.SaveChangesAsync();

                // Gửi email thông báo hủy vé
                if (!string.IsNullOrEmpty(ve.Email))
                {
                    var emailSubject = $"Xác nhận hủy vé - {ve.MaVe}";
                    var emailBody = $@"
                        <h3>Xác nhận hủy vé</h3>
                        <p>Kính chào {ve.TenKhach},</p>
                        <p>Vé của bạn đã được hủy thành công:</p>
                        <ul>
                            <li>Mã vé: {ve.MaVe}</li>
                            <li>Chuyến xe: {ve.ChuyenXe.DiemDi} - {ve.ChuyenXe.DiemDen}</li>
                            <li>Ngày khởi hành: {ve.ChuyenXe.NgayKhoiHanh:dd/MM/yyyy HH:mm}</li>
                            <li>Phí hủy: {model.PhiHuy:N0} VNĐ</li>
                            <li>Số tiền hoàn lại: {model.SoTienHoanLai:N0} VNĐ</li>
                        </ul>
                        <p>Số tiền hoàn lại sẽ được chuyển về tài khoản của bạn trong 3-5 ngày làm việc.</p>
                        <p>Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!</p>
                    ";

                    await _emailService.SendEmailAsync(ve.Email, emailSubject, emailBody);
                }

                TempData["Success"] = "Hủy vé thành công. Số tiền hoàn lại sẽ được chuyển về tài khoản của bạn trong 3-5 ngày làm việc.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi hủy vé {VeId}", id);
                TempData["Error"] = "Có lỗi xảy ra khi hủy vé. Vui lòng thử lại.";
                return View(model);
            }
        }

        // GET: MyTickets/Rate/{id} - Đánh giá chuyến đi
        public async Task<IActionResult> Rate(int id)
        {
            int? userId = HttpContext.Session.GetInt32("UserId");
            if (!userId.HasValue)
            {
                TempData["Error"] = "Vui lòng đăng nhập";
                return RedirectToAction("DangNhap", "TaiKhoan");
            }

            var ve = await _context.Ves
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.Xe)
                .Include(v => v.ChuyenXe)
                .ThenInclude(c => c.TuyenDuong)
                .Include(v => v.DanhGias)
                .FirstOrDefaultAsync(v => v.VeId == id && v.NguoiDungId == userId.Value);

            if (ve == null)
            {
                TempData["Error"] = "Không tìm thấy vé";
                return RedirectToAction("Index");
            }

            if (ve.TrangThai != TrangThaiVe.DaDat || ve.ChuyenXe.NgayKhoiHanh > DateTime.Now)
            {
                TempData["Error"] = "Chỉ có thể đánh giá sau khi chuyến xe hoàn thành";
                return RedirectToAction("Details", new { id });
            }

            if (ve.DanhGias.Any())
            {
                TempData["Error"] = "Bạn đã đánh giá chuyến đi này rồi";
                return RedirectToAction("Details", new { id });
            }

            var viewModel = new RatingViewModel
            {
                VeId = ve.VeId,
                MaVe = ve.MaVe,
                ChuyenXe = ve.ChuyenXe
            };

            return View(viewModel);
        }

        // POST: MyTickets/Rate/{id} - Xử lý đánh giá
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Rate(int id, RatingViewModel model)
        {
            int? userId = HttpContext.Session.GetInt32("UserId");
            if (!userId.HasValue)
            {
                TempData["Error"] = "Vui lòng đăng nhập";
                return RedirectToAction("DangNhap", "TaiKhoan");
            }

            if (!ModelState.IsValid)
            {
                var ve = await _context.Ves
                    .Include(v => v.ChuyenXe)
                    .ThenInclude(c => c.Xe)
                    .Include(v => v.ChuyenXe)
                    .ThenInclude(c => c.TuyenDuong)
                    .FirstOrDefaultAsync(v => v.VeId == id && v.NguoiDungId == userId.Value);

                if (ve != null)
                {
                    model.ChuyenXe = ve.ChuyenXe;
                    model.MaVe = ve.MaVe;
                }
                return View(model);
            }

            try
            {
                var ve = await _context.Ves
                    .Include(v => v.DanhGias)
                    .FirstOrDefaultAsync(v => v.VeId == id && v.NguoiDungId == userId.Value);

                if (ve == null)
                {
                    TempData["Error"] = "Không tìm thấy vé";
                    return RedirectToAction("Index");
                }

                if (ve.DanhGias.Any())
                {
                    TempData["Error"] = "Bạn đã đánh giá chuyến đi này rồi";
                    return RedirectToAction("Details", new { id });
                }

                var danhGia = new DanhGiaChuyenDi
                {
                    VeId = ve.VeId,
                    NguoiDungId = userId.Value,
                    DiemDanhGia = model.DiemDanhGia,
                    NhanXet = model.NhanXet,
                    ChatLuongXe = model.ChatLuongXe,
                    ThaiDoTaiXe = model.ThaiDoTaiXe,
                    DungGio = model.DungGio,
                    GiaCa = model.GiaCa,
                    NgayDanhGia = DateTime.Now,
                    TrangThaiHienThi = true
                };

                _context.DanhGiaChuyenDis.Add(danhGia);
                await _context.SaveChangesAsync();

                TempData["Success"] = "Cảm ơn bạn đã đánh giá chuyến đi!";
                return RedirectToAction("Details", new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi lưu đánh giá cho vé {VeId}", id);
                TempData["Error"] = "Có lỗi xảy ra khi lưu đánh giá. Vui lòng thử lại.";
                return View(model);
            }
        }

        // Helper method để tạo QR Code (sử dụng Google Charts API)
        private string GenerateQRCode(Ve ve)
        {
            var qrData = $"TICKET:{ve.MaVe}|TRIP:{ve.ChuyenXe.DiemDi}-{ve.ChuyenXe.DiemDen}|DATE:{ve.ChuyenXe.NgayKhoiHanh:yyyy-MM-dd HH:mm}|SEAT:{ve.ChoNgoi.SoGhe}|PASSENGER:{ve.TenKhach}";

            // Sử dụng Google Charts API để tạo QR code
            var encodedData = Uri.EscapeDataString(qrData);
            var qrUrl = $"https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl={encodedData}";

            // Trả về URL thay vì base64 image
            return qrUrl;
        }
    }
}

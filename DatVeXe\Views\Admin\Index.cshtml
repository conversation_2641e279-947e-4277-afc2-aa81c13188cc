@model DatVeXe.Controllers.AdminDashboardViewModel
@{
    ViewData["Title"] = "Quản trị hệ thống";
}

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-5 fw-bold text-primary">
            <i class="bi bi-speedometer2 me-3"></i>Quản trị hệ thống
        </h1>
        <div class="text-end">
            <small class="text-muted">Cập nhật lúc: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</small>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-5">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-3 p-3">
                                <i class="bi bi-people text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Người dùng</h6>
                            <h3 class="mb-0 fw-bold">@Model.TotalUsers</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-3 p-3">
                                <i class="bi bi-truck text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Xe</h6>
                            <h3 class="mb-0 fw-bold">@Model.TotalVehicles</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-3 p-3">
                                <i class="bi bi-geo-alt text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Tuyến đường</h6>
                            <h3 class="mb-0 fw-bold">@Model.TotalRoutes</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-3 p-3">
                                <i class="bi bi-calendar-event text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Chuyến xe</h6>
                            <h3 class="mb-0 fw-bold">@Model.TotalTrips</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-gradient rounded-3 p-3">
                                <i class="bi bi-ticket-perforated text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Vé đã đặt</h6>
                            <h3 class="mb-0 fw-bold">@Model.TotalBookings</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-secondary bg-gradient rounded-3 p-3">
                                <i class="bi bi-currency-dollar text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Doanh thu</h6>
                            <h3 class="mb-0 fw-bold">@string.Format("{0:N0}", Model.TotalRevenue) VNĐ</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning me-2"></i>Thao tác nhanh
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a asp-action="CreateVehicle" class="btn btn-outline-primary w-100 py-3">
                                <i class="bi bi-plus-circle me-2"></i>Thêm xe mới
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a asp-action="CreateRoute" class="btn btn-outline-success w-100 py-3">
                                <i class="bi bi-plus-circle me-2"></i>Thêm tuyến đường
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a asp-action="CreateTrip" class="btn btn-outline-warning w-100 py-3">
                                <i class="bi bi-plus-circle me-2"></i>Thêm chuyến xe
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a asp-action="CreatePromotion" class="btn btn-outline-info w-100 py-3">
                                <i class="bi bi-plus-circle me-2"></i>Thêm khuyến mãi
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Management Links -->
    <div class="row g-4 mb-5">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="bi bi-truck display-4 text-primary mb-3"></i>
                    <h5 class="card-title">Quản lý xe</h5>
                    <p class="card-text text-muted">Thêm, sửa, xóa thông tin xe</p>
                    <a asp-action="Vehicles" class="btn btn-primary">Quản lý</a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="bi bi-geo-alt display-4 text-success mb-3"></i>
                    <h5 class="card-title">Quản lý tuyến đường</h5>
                    <p class="card-text text-muted">Thêm, sửa, xóa tuyến đường</p>
                    <a asp-action="Routes" class="btn btn-success">Quản lý</a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-event display-4 text-warning mb-3"></i>
                    <h5 class="card-title">Quản lý chuyến xe</h5>
                    <p class="card-text text-muted">Thêm, sửa, xóa chuyến xe</p>
                    <a asp-action="Trips" class="btn btn-warning">Quản lý</a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="bi bi-ticket-perforated display-4 text-danger mb-3"></i>
                    <h5 class="card-title">Quản lý đặt vé</h5>
                    <p class="card-text text-muted">Xem, quản lý đặt vé</p>
                    <a asp-action="Bookings" class="btn btn-danger">Quản lý</a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="bi bi-gift display-4 text-info mb-3"></i>
                    <h5 class="card-title">Quản lý khuyến mãi</h5>
                    <p class="card-text text-muted">Thêm, sửa, xóa khuyến mãi</p>
                    <a asp-action="Promotions" class="btn btn-info">Quản lý</a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <i class="bi bi-people display-4 text-secondary mb-3"></i>
                    <h5 class="card-title">Quản lý người dùng</h5>
                    <p class="card-text text-muted">Xem, quản lý người dùng</p>
                    <a href="#" class="btn btn-secondary">Quản lý</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Bookings -->
    @if (Model.RecentBookings.Any())
    {
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>Đặt vé gần đây
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Mã vé</th>
                                        <th>Khách hàng</th>
                                        <th>Chuyến xe</th>
                                        <th>Ngày đặt</th>
                                        <th>Giá vé</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var booking in Model.RecentBookings)
                                    {
                                        <tr>
                                            <td><strong>@booking.MaVe</strong></td>
                                            <td>
                                                @booking.TenKhach
                                                <br><small class="text-muted">@booking.SoDienThoai</small>
                                            </td>
                                            <td>
                                                @booking.ChuyenXe.DiemDi → @booking.ChuyenXe.DiemDen
                                                <br><small class="text-muted">@booking.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</small>
                                            </td>
                                            <td>@booking.NgayDat.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td><strong>@string.Format("{0:N0}", booking.GiaVe - booking.SoTienGiam) VNĐ</strong></td>
                                            <td>
                                                @{
                                                    var badgeClass = booking.TrangThai == TrangThaiVe.DaDat ? "bg-success" : "bg-danger";
                                                    var statusText = booking.TrangThai == TrangThaiVe.DaDat ? "Đã đặt" : "Đã hủy";
                                                }
                                                <span class="badge @badgeClass">@statusText</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .card {
        border-radius: 15px;
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }
    
    .btn {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn:hover {
        transform: translateY(-1px);
    }
</style>

# 🚌 HƯỚNG DẪN SỬ DỤNG HỆ THỐNG ĐẶT VÉ XE

## 📋 TỔNG QUAN HỆ THỐNG

Hệ thống đặt vé xe hoàn chỉnh với đầy đủ tính năng enterprise:
- **Giao diện**: Manchester City theme chuyên nghiệp
- **Chức năng**: Đặt vé online với chọn chỗ ngồi
- **Quản lý**: Admin dashboard với thống kê chi tiết
- **Email**: Xác nhận và thông báo tự động
- **Responsive**: Hoạt động tốt trên mọi thiết bị

---

## 👤 DÀNH CHO KHÁCH HÀNG

### 🔐 Đăng ký/Đăng nhập
1. **Truy cập**: `http://localhost:5057`
2. **Đăng ký**: Click "Đăng nhập" → "Đăng ký tài khoản mới"
3. **Đăng nhập**: Sử dụng email và mật khẩu

### 🔍 Tìm kiếm và đặt vé
1. **Tì<PERSON> chuyến xe**: 
   - Click "Tìm chuyến xe" hoặc truy cập `/ChuyenXe/Search`
   - Chọn điểm đi, điểm đến, ngày khởi hành
   - Click "Tìm kiếm"

2. **Chọn chỗ ngồi**:
   - Click nút ghế (🪑) trên kết quả tìm kiếm
   - Xem sơ đồ ghế xe với 4 trạng thái:
     - 🟢 **Còn trống**: Có thể chọn
     - 🔵 **Đang chọn**: Ghế bạn đã chọn
     - 🔴 **Đã đặt**: Không thể chọn
     - ⚫ **Không khả dụng**: Ghế hỏng/bảo trì

3. **Đặt vé**:
   - Click vào ghế muốn chọn
   - Điền thông tin: Tên, SĐT, Email (tùy chọn)
   - Click "Đặt vé ngay"
   - Nhận email xác nhận (nếu có email)

### 📱 Quản lý vé cá nhân
1. **Xem vé**: Click "Vé của tôi" hoặc `/Ve`
2. **Dashboard**: `/TaiKhoan/Dashboard` - Thống kê cá nhân
3. **Lịch sử**: `/TaiKhoan/LichSuDatVe` - Tất cả vé đã đặt
4. **Hủy vé**: Click "Hủy vé" (chỉ trước 2h khởi hành)

---

## 👨‍💼 DÀNH CHO ADMIN

### 🔑 Đăng nhập Admin
- **Email**: `<EMAIL>`
- **Mật khẩu**: `Admin123!`

### 📊 Dashboard Admin
**Truy cập**: `/Home/Dashboard`
- Thống kê tổng quan: Người dùng, xe, tuyến đường, vé
- Top chuyến xe phổ biến
- Biểu đồ doanh thu
- Top xe hiệu quả nhất

### 🗺️ Quản lý tuyến đường
**Truy cập**: `/TuyenDuong`
- **Danh sách tuyến**: Xem tất cả tuyến đường với thống kê
- **Chi tiết tuyến**: Click "Xem chi tiết" để xem:
  - Thống kê chi tiết từng tuyến
  - Danh sách chuyến xe
  - Biểu đồ doanh thu và tỷ lệ lấp đầy
- **Thống kê tổng**: `/TuyenDuong/ThongKe`
  - Top 10 tuyến phổ biến
  - Thống kê theo tháng
  - Biểu đồ doanh thu

### 🎫 Quản lý vé nâng cao
**Truy cập**: `/Ve`
- **Filter mạnh mẽ**:
  - Trạng thái: Đã đặt/Đã hủy
  - Khoảng thời gian
  - Điểm đi/đến
  - Mã vé, tên khách
- **Thống kê**: Tổng vé, doanh thu thực tế
- **Xuất báo cáo**: Dữ liệu chi tiết

### 🚌 Quản lý xe và chuyến xe
1. **Quản lý xe**: `/Xe`
   - Thêm/sửa/xóa xe
   - Xem sơ đồ ghế: `/ChoNgoi/SoDoGhe/{xeId}`
   
2. **Quản lý chuyến xe**: `/ChuyenXe`
   - Thêm/sửa chuyến xe
   - Xem chi tiết và vé đã bán

### 👥 Quản lý người dùng
**Truy cập**: `/NguoiDung`
- Danh sách tất cả người dùng
- Thống kê hoạt động
- Khóa/mở khóa tài khoản

### 📈 Báo cáo và thống kê
**Truy cập**: `/BaoCao`
- Doanh thu theo ngày/tháng/năm
- Thống kê theo tuyến đường
- Hiệu quả sử dụng xe
- Xuất báo cáo Excel/PDF

---

## 🛠️ TÍNH NĂNG NÂNG CAO

### 📧 Hệ thống Email
- **Xác nhận đặt vé**: Gửi tự động khi đặt vé thành công
- **Thông báo hủy vé**: Gửi khi hủy vé
- **Template HTML**: Thiết kế chuyên nghiệp
- **Thông tin chi tiết**: Mã vé, ghế, chuyến xe, giá

### 🪑 Chọn chỗ ngồi
- **Sơ đồ trực quan**: Hiển thị layout thực tế của xe
- **Giữ chỗ tạm thời**: 5 phút khi đang chọn
- **Real-time**: Cập nhật trạng thái ghế ngay lập tức
- **Responsive**: Hoạt động tốt trên mobile

### 🔒 Bảo mật
- **Mã hóa mật khẩu**: SHA-256
- **Session management**: Tự động đăng xuất
- **Role-based access**: Phân quyền admin/user
- **Validation**: Kiểm tra dữ liệu đầu vào

---

## 🚀 TRIỂN KHAI

### Yêu cầu hệ thống
- **.NET 8.0** hoặc cao hơn
- **SQL Server** (LocalDB hoặc SQL Server)
- **IIS** (cho production)

### Cấu hình Email
Cập nhật `appsettings.json`:
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderName": "Hệ thống đặt vé xe",
    "Username": "<EMAIL>",
    "Password": "your-app-password",
    "EnableSsl": true
  }
}
```

### Chạy ứng dụng
```bash
cd "D:\Đặt vé xe"
dotnet run --project DatVeXe
```

### URL truy cập
- **Trang chủ**: `http://localhost:5057`
- **Admin**: `http://localhost:5057/Home/Dashboard`

---

## 📞 HỖ TRỢ

### Tài khoản test
**Admin**:
- Email: `<EMAIL>`
- Password: `Admin123!`

**User**:
- Email: `<EMAIL>`
- Password: `123456`

### Liên hệ
- **Email hỗ trợ**: `<EMAIL>`
- **Tài liệu**: Xem file README.md
- **Source code**: Thư mục dự án

---

## 🎯 TÍNH NĂNG CHÍNH

✅ **Đặt vé online với chọn chỗ ngồi**
✅ **Dashboard admin với biểu đồ**
✅ **Quản lý tuyến đường chi tiết**
✅ **Email xác nhận tự động**
✅ **Thống kê doanh thu nâng cao**
✅ **Filter và tìm kiếm mạnh mẽ**
✅ **Responsive design**
✅ **Manchester City theme**
✅ **Role-based security**
✅ **Real-time seat selection**

Hệ thống đã sẵn sàng cho triển khai thương mại! 🚌✨

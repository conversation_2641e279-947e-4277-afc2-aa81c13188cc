@model DatVeXe.Controllers.DatabaseStatusViewModel
@{
    ViewData["Title"] = "Trạng thái Database";
}

<div class="container py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 fw-bold text-primary">
            <i class="bi bi-database me-3"></i>Trạng thái Database
        </h1>
        <div>
            <button onclick="testConnection()" class="btn btn-outline-primary me-2">
                <i class="bi bi-arrow-clockwise me-1"></i>Test kết nối
            </button>
            <button onclick="createSampleData()" class="btn btn-success">
                <i class="bi bi-plus-circle me-1"></i>Tạo dữ liệu mẫu
            </button>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger">
            <h5><i class="bi bi-exclamation-triangle me-2"></i>Lỗi kết nối Database</h5>
            <p class="mb-0">@Model.ErrorMessage</p>
        </div>
    }

    <!-- Connection Status -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header @(Model.IsConnected ? "bg-success" : "bg-danger") text-white">
                    <h5 class="mb-0">
                        <i class="bi @(Model.IsConnected ? "bi-check-circle" : "bi-x-circle") me-2"></i>
                        Trạng thái kết nối: @(Model.IsConnected ? "Thành công" : "Thất bại")
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.IsConnected)
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Tên Database:</strong> @Model.DatabaseName
                            </div>
                            <div class="col-md-6">
                                <strong>Connection String:</strong>
                                <small class="text-muted">@(Model.ConnectionString?.Substring(0, Math.Min(80, Model.ConnectionString?.Length ?? 0)))...</small>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    @if (Model.IsConnected)
    {
        <!-- Data Statistics -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="bi bi-people display-4 text-primary mb-3"></i>
                        <h3 class="fw-bold">@Model.UserCount</h3>
                        <p class="text-muted mb-0">Người dùng</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="bi bi-truck display-4 text-success mb-3"></i>
                        <h3 class="fw-bold">@Model.VehicleCount</h3>
                        <p class="text-muted mb-0">Xe</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="bi bi-geo-alt display-4 text-warning mb-3"></i>
                        <h3 class="fw-bold">@Model.RouteCount</h3>
                        <p class="text-muted mb-0">Tuyến đường</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="bi bi-calendar-event display-4 text-info mb-3"></i>
                        <h3 class="fw-bold">@Model.TripCount</h3>
                        <p class="text-muted mb-0">Chuyến xe</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="bi bi-ticket-perforated display-4 text-danger mb-3"></i>
                        <h3 class="fw-bold">@Model.BookingCount</h3>
                        <p class="text-muted mb-0">Đặt vé</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="bi bi-square display-4 text-secondary mb-3"></i>
                        <h3 class="fw-bold">@Model.SeatCount</h3>
                        <p class="text-muted mb-0">Ghế</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="bi bi-credit-card display-4 text-primary mb-3"></i>
                        <h3 class="fw-bold">@Model.PaymentCount</h3>
                        <p class="text-muted mb-0">Thanh toán</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="bi bi-gift display-4 text-success mb-3"></i>
                        <h3 class="fw-bold">@Model.PromotionCount</h3>
                        <p class="text-muted mb-0">Khuyến mãi</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Data Tables -->
        @if (Model.SampleVehicles.Any())
        {
            <div class="row g-4 mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="bi bi-truck me-2"></i>Xe trong hệ thống (5 xe đầu tiên)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Biển số</th>
                                            <th>Loại xe</th>
                                            <th>Số ghế</th>
                                            <th>Hãng xe</th>
                                            <th>Trạng thái</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var vehicle in Model.SampleVehicles)
                                        {
                                            <tr>
                                                <td><strong>@vehicle.BienSo</strong></td>
                                                <td>@vehicle.LoaiXe</td>
                                                <td>@vehicle.SoGhe</td>
                                                <td>@vehicle.HangXe</td>
                                                <td>
                                                    <span class="badge @(vehicle.TrangThaiHoatDong ? "bg-success" : "bg-danger")">
                                                        @(vehicle.TrangThaiHoatDong ? "Hoạt động" : "Ngừng hoạt động")
                                                    </span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

        @if (Model.SampleRoutes.Any())
        {
            <div class="row g-4 mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="bi bi-geo-alt me-2"></i>Tuyến đường (5 tuyến đầu tiên)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Điểm đi</th>
                                            <th>Điểm đến</th>
                                            <th>Khoảng cách</th>
                                            <th>Thời gian dự kiến</th>
                                            <th>Trạng thái</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var route in Model.SampleRoutes)
                                        {
                                            <tr>
                                                <td><strong>@route.DiemDi</strong></td>
                                                <td><strong>@route.DiemDen</strong></td>
                                                <td>@route.KhoangCach km</td>
                                                <td>@route.ThoiGianDuKien.ToString(@"hh\:mm")</td>
                                                <td>
                                                    <span class="badge @(route.TrangThaiHoatDong ? "bg-success" : "bg-danger")">
                                                        @(route.TrangThaiHoatDong ? "Hoạt động" : "Ngừng hoạt động")
                                                    </span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

        @if (Model.SampleTrips.Any())
        {
            <div class="row g-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="bi bi-calendar-event me-2"></i>Chuyến xe (5 chuyến đầu tiên)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Tuyến đường</th>
                                            <th>Xe</th>
                                            <th>Ngày khởi hành</th>
                                            <th>Giá vé</th>
                                            <th>Trạng thái</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var trip in Model.SampleTrips)
                                        {
                                            <tr>
                                                <td>
                                                    <strong>@trip.DiemDi → @trip.DiemDen</strong>
                                                </td>
                                                <td>
                                                    @trip.Xe?.BienSo
                                                    <br><small class="text-muted">@trip.Xe?.LoaiXe</small>
                                                </td>
                                                <td>@trip.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                                <td><strong>@string.Format("{0:N0}", trip.Gia) VNĐ</strong></td>
                                                <td>
                                                    <span class="badge @(trip.TrangThaiChuyenXe == TrangThaiChuyenXe.HoatDong ? "bg-success" : "bg-danger")">
                                                        @trip.TrangThaiChuyenXe
                                                    </span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

        @if (!Model.SampleVehicles.Any() && !Model.SampleRoutes.Any() && !Model.SampleTrips.Any())
        {
            <div class="alert alert-info">
                <h5><i class="bi bi-info-circle me-2"></i>Database trống</h5>
                <p class="mb-0">Database đã kết nối thành công nhưng chưa có dữ liệu. Bạn có thể tạo dữ liệu mẫu hoặc thêm dữ liệu thông qua trang quản trị.</p>
            </div>
        }
    }
</div>

@section Scripts {
    <script>
        function testConnection() {
            $.get('/Database/TestConnection')
                .done(function(data) {
                    if (data.success) {
                        alert('✅ ' + data.message + '\nDatabase: ' + data.databaseName);
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .fail(function() {
                    alert('❌ Không thể test kết nối');
                });
        }

        function createSampleData() {
            if (confirm('Bạn có chắc muốn tạo dữ liệu mẫu? Thao tác này sẽ thêm xe, tuyến đường và chuyến xe mẫu vào database.')) {
                $.get('/Database/CreateSampleData')
                    .done(function(data) {
                        if (data.success) {
                            alert('✅ ' + data.message);
                            location.reload();
                        } else {
                            alert('❌ ' + data.message);
                        }
                    })
                    .fail(function() {
                        alert('❌ Không thể tạo dữ liệu mẫu');
                    });
            }
        }
    </script>
}

<style>
    .card {
        border-radius: 15px;
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }
</style>

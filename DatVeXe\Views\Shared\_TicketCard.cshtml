@model DatVeXe.Models.Ve

@{
    var now = DateTime.Now;
    var isUpcoming = Model.ChuyenXe.NgayKhoiHanh > now;
    var isCompleted = Model.ChuyenXe.NgayKhoiHanh <= now && Model.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat;
    var isCancelled = Model.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy;
    var canCancel = Model.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat && Model.ChuyenXe.NgayKhoiHanh > now.AddHours(2);
    var canRate = isCompleted && !Model.DanhGias.Any();
    
    var statusClass = isCancelled ? "border-danger" : isCompleted ? "border-success" : "border-primary";
    var statusBadge = isCancelled ? "bg-danger" : isCompleted ? "bg-success" : "bg-primary";
    var statusText = isCancelled ? "Đã hủy" : isCompleted ? "Hoàn thành" : "Đã đặt";
}

<div class="card h-100 @statusClass" style="border-width: 2px;">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <div>
            <h6 class="mb-0 fw-bold">@Model.MaVe</h6>
            <small class="text-muted">Đặt ngày @Model.NgayDat.ToString("dd/MM/yyyy HH:mm")</small>
        </div>
        <span class="badge @statusBadge">@statusText</span>
    </div>
    
    <div class="card-body">
        <!-- Route Info -->
        <div class="d-flex align-items-center mb-3">
            <div class="text-center">
                <div class="fw-bold text-primary">@Model.ChuyenXe.DiemDi</div>
                <small class="text-muted">Điểm đi</small>
            </div>
            <div class="flex-fill text-center mx-3">
                <i class="bi bi-arrow-right text-primary fs-4"></i>
            </div>
            <div class="text-center">
                <div class="fw-bold text-primary">@Model.ChuyenXe.DiemDen</div>
                <small class="text-muted">Điểm đến</small>
            </div>
        </div>

        <!-- Trip Details -->
        <div class="row g-3 mb-3">
            <div class="col-6">
                <div class="d-flex align-items-center">
                    <i class="bi bi-calendar-event text-info me-2"></i>
                    <div>
                        <div class="fw-semibold">@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                        <small class="text-muted">@Model.ChuyenXe.NgayKhoiHanh.ToString("HH:mm")</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="d-flex align-items-center">
                    <i class="bi bi-geo-alt text-warning me-2"></i>
                    <div>
                        <div class="fw-semibold">Ghế @Model.ChoNgoi.SoGhe</div>
                        <small class="text-muted">@Model.ChoNgoi.LoaiGhe</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vehicle Info -->
        <div class="row g-3 mb-3">
            <div class="col-6">
                <div class="d-flex align-items-center">
                    <i class="bi bi-truck text-success me-2"></i>
                    <div>
                        <div class="fw-semibold">@Model.ChuyenXe.Xe.LoaiXe</div>
                        <small class="text-muted">@Model.ChuyenXe.Xe.BienSo</small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="d-flex align-items-center">
                    <i class="bi bi-person text-primary me-2"></i>
                    <div>
                        <div class="fw-semibold">@Model.TenKhach</div>
                        <small class="text-muted">@Model.SoDienThoai</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Price Info -->
        <div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
            <div>
                <small class="text-muted">Giá vé:</small>
                <div class="fw-bold">@string.Format("{0:N0}", Model.GiaVe) VNĐ</div>
            </div>
            @if (Model.SoTienGiam > 0)
            {
                <div class="text-end">
                    <small class="text-muted">Giảm giá:</small>
                    <div class="fw-bold text-success">-@string.Format("{0:N0}", Model.SoTienGiam) VNĐ</div>
                </div>
            }
            <div class="text-end">
                <small class="text-muted">Thành tiền:</small>
                <div class="fw-bold text-primary fs-5">@string.Format("{0:N0}", Model.GiaVe - Model.SoTienGiam) VNĐ</div>
            </div>
        </div>

        @if (Model.KhuyenMai != null)
        {
            <div class="alert alert-success py-2 mb-3">
                <i class="bi bi-gift me-2"></i>
                <small>Đã áp dụng: <strong>@Model.KhuyenMai.TenKhuyenMai</strong></small>
            </div>
        }
    </div>

    <div class="card-footer bg-white border-top-0">
        <div class="d-flex gap-2 flex-wrap">
            <a asp-controller="MyTickets" asp-action="Details" asp-route-id="@Model.VeId" 
               class="btn btn-outline-primary btn-sm flex-fill">
                <i class="bi bi-eye me-1"></i>Chi tiết
            </a>
            
            @if (canCancel)
            {
                <a asp-controller="MyTickets" asp-action="Cancel" asp-route-id="@Model.VeId" 
                   class="btn btn-outline-danger btn-sm">
                    <i class="bi bi-x-circle me-1"></i>Hủy vé
                </a>
            }
            
            @if (canRate)
            {
                <a asp-controller="MyTickets" asp-action="Rate" asp-route-id="@Model.VeId" 
                   class="btn btn-outline-warning btn-sm">
                    <i class="bi bi-star me-1"></i>Đánh giá
                </a>
            }
        </div>
        
        @if (isUpcoming)
        {
            <div class="mt-2">
                @{
                    var timeUntilDeparture = Model.ChuyenXe.NgayKhoiHanh - now;
                    var daysLeft = (int)timeUntilDeparture.TotalDays;
                    var hoursLeft = timeUntilDeparture.Hours;
                }
                
                @if (daysLeft > 0)
                {
                    <small class="text-info">
                        <i class="bi bi-clock me-1"></i>Còn @daysLeft ngày @hoursLeft giờ
                    </small>
                }
                else if (timeUntilDeparture.TotalHours > 2)
                {
                    <small class="text-warning">
                        <i class="bi bi-clock me-1"></i>Còn @((int)timeUntilDeparture.TotalHours) giờ @timeUntilDeparture.Minutes phút
                    </small>
                }
                else if (timeUntilDeparture.TotalHours > 0)
                {
                    <small class="text-danger">
                        <i class="bi bi-exclamation-triangle me-1"></i>Sắp khởi hành (@((int)timeUntilDeparture.TotalHours) giờ @timeUntilDeparture.Minutes phút)
                    </small>
                }
            </div>
        }
    </div>
</div>

<style>
    .card {
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .btn-sm {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
</style>

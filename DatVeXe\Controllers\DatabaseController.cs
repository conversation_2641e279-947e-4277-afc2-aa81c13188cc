using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;

namespace DatVeXe.Controllers
{
    public class DatabaseController : Controller
    {
        private readonly DatVeXeContext _context;
        private readonly ILogger<DatabaseController> _logger;

        public DatabaseController(DatVeXeContext context, ILogger<DatabaseController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: Database/Status - Kiểm tra trạng thái database
        public async Task<IActionResult> Status()
        {
            try
            {
                var status = new DatabaseStatusViewModel
                {
                    IsConnected = await _context.Database.CanConnectAsync(),
                    DatabaseName = _context.Database.GetDbConnection().Database,
                    ConnectionString = _context.Database.GetConnectionString(),
                    
                    // Đếm số lượng dữ liệu
                    UserCount = await _context.NguoiDungs.CountAsync(),
                    VehicleCount = await _context.Xes.CountAsync(),
                    RouteCount = await _context.TuyenDuongs.CountAsync(),
                    TripCount = await _context.ChuyenXes.CountAsync(),
                    BookingCount = await _context.Ves.CountAsync(),
                    SeatCount = await _context.ChoNgois.CountAsync(),
                    PaymentCount = await _context.ThanhToans.CountAsync(),
                    PromotionCount = await _context.KhuyenMais.CountAsync(),
                    RatingCount = await _context.DanhGiaChuyenDis.CountAsync(),

                    // Lấy dữ liệu mẫu
                    SampleUsers = await _context.NguoiDungs.Take(5).ToListAsync(),
                    SampleVehicles = await _context.Xes.Take(5).ToListAsync(),
                    SampleRoutes = await _context.TuyenDuongs.Take(5).ToListAsync(),
                    SampleTrips = await _context.ChuyenXes
                        .Include(c => c.Xe)
                        .Include(c => c.TuyenDuong)
                        .Take(5)
                        .ToListAsync(),
                    SampleBookings = await _context.Ves
                        .Include(v => v.ChuyenXe)
                        .Include(v => v.NguoiDung)
                        .Take(5)
                        .ToListAsync()
                };

                return View(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi kiểm tra trạng thái database");
                
                var errorStatus = new DatabaseStatusViewModel
                {
                    IsConnected = false,
                    ErrorMessage = ex.Message
                };
                
                return View(errorStatus);
            }
        }

        // GET: Database/TestConnection - Test kết nối database
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                var canConnect = await _context.Database.CanConnectAsync();
                var connectionString = _context.Database.GetConnectionString();
                var databaseName = _context.Database.GetDbConnection().Database;

                return Json(new
                {
                    success = canConnect,
                    message = canConnect ? "Kết nối database thành công" : "Không thể kết nối database",
                    databaseName = databaseName,
                    connectionString = connectionString?.Substring(0, Math.Min(50, connectionString.Length)) + "..."
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = "Lỗi kết nối: " + ex.Message
                });
            }
        }

        // GET: Database/CreateSampleData - Tạo dữ liệu mẫu cơ bản
        public async Task<IActionResult> CreateSampleData()
        {
            try
            {
                // Kiểm tra xem đã có dữ liệu chưa
                var hasData = await _context.Xes.AnyAsync() || 
                             await _context.TuyenDuongs.AnyAsync() || 
                             await _context.ChuyenXes.AnyAsync();

                if (hasData)
                {
                    return Json(new { success = false, message = "Database đã có dữ liệu. Không cần tạo mẫu." });
                }

                // Tạo dữ liệu mẫu cơ bản
                await CreateBasicSampleData();

                return Json(new { success = true, message = "Đã tạo dữ liệu mẫu cơ bản thành công!" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi tạo dữ liệu mẫu");
                return Json(new { success = false, message = "Lỗi: " + ex.Message });
            }
        }

        private async Task CreateBasicSampleData()
        {
            // Tạo một số xe mẫu
            var vehicles = new List<Xe>
            {
                new Xe { BienSo = "29A-12345", LoaiXe = "Limousine", SoGhe = 22, HangXe = "Hyundai", MauXe = "Trắng", NamSanXuat = 2023, TrangThaiHoatDong = true },
                new Xe { BienSo = "30B-67890", LoaiXe = "Giường nằm", SoGhe = 40, HangXe = "Thaco", MauXe = "Xanh", NamSanXuat = 2022, TrangThaiHoatDong = true },
                new Xe { BienSo = "31C-11111", LoaiXe = "Ghế ngồi", SoGhe = 45, HangXe = "Isuzu", MauXe = "Đỏ", NamSanXuat = 2021, TrangThaiHoatDong = true }
            };

            _context.Xes.AddRange(vehicles);
            await _context.SaveChangesAsync();

            // Tạo ghế cho từng xe
            foreach (var vehicle in vehicles)
            {
                var seats = new List<ChoNgoi>();
                for (int i = 1; i <= vehicle.SoGhe; i++)
                {
                    seats.Add(new ChoNgoi
                    {
                        XeId = vehicle.XeId,
                        SoGhe = i.ToString(),
                        LoaiGhe = vehicle.LoaiXe,
                        TrangThai = TrangThaiChoNgoi.Trong
                    });
                }
                _context.ChoNgois.AddRange(seats);
            }

            // Tạo tuyến đường mẫu
            var routes = new List<TuyenDuong>
            {
                new TuyenDuong { DiemDi = "Hà Nội", DiemDen = "Hồ Chí Minh", KhoangCach = 1700, ThoiGianDuKien = TimeSpan.FromHours(24), TrangThaiHoatDong = true },
                new TuyenDuong { DiemDi = "Hà Nội", DiemDen = "Đà Nẵng", KhoangCach = 800, ThoiGianDuKien = TimeSpan.FromHours(12), TrangThaiHoatDong = true },
                new TuyenDuong { DiemDi = "Hồ Chí Minh", DiemDen = "Đà Lạt", KhoangCach = 300, ThoiGianDuKien = TimeSpan.FromHours(6), TrangThaiHoatDong = true }
            };

            _context.TuyenDuongs.AddRange(routes);
            await _context.SaveChangesAsync();

            // Tạo chuyến xe mẫu
            var trips = new List<ChuyenXe>();
            var random = new Random();

            for (int i = 0; i < 10; i++)
            {
                var vehicle = vehicles[random.Next(vehicles.Count)];
                var route = routes[random.Next(routes.Count)];
                var departureDate = DateTime.Now.AddDays(random.Next(1, 30)).AddHours(random.Next(6, 22));

                trips.Add(new ChuyenXe
                {
                    XeId = vehicle.XeId,
                    TuyenDuongId = route.TuyenDuongId,
                    NgayKhoiHanh = departureDate,
                    Gia = random.Next(200000, 800000),
                    DiemDi = route.DiemDi,
                    DiemDen = route.DiemDen,
                    TrangThaiChuyenXe = TrangThaiChuyenXe.HoatDong
                });
            }

            _context.ChuyenXes.AddRange(trips);
            await _context.SaveChangesAsync();

            // Tạo khuyến mãi mẫu
            var promotions = new List<KhuyenMai>
            {
                new KhuyenMai
                {
                    TenKhuyenMai = "Giảm 20% cho khách hàng mới",
                    MaKhuyenMai = "NEW20",
                    MoTa = "Giảm 20% cho khách hàng đăng ký lần đầu",
                    PhanTramGiam = 20,
                    SoTienGiamToiDa = 100000,
                    GiaTriDonHangToiThieu = 200000,
                    NgayBatDau = DateTime.Now,
                    NgayKetThuc = DateTime.Now.AddDays(30),
                    TrangThaiHoatDong = true,
                    ApDungChoKhachHangMoi = true,
                    NgayTao = DateTime.Now
                }
            };

            _context.KhuyenMais.AddRange(promotions);
            await _context.SaveChangesAsync();
        }
    }

    // ViewModel cho trạng thái database
    public class DatabaseStatusViewModel
    {
        public bool IsConnected { get; set; }
        public string? DatabaseName { get; set; }
        public string? ConnectionString { get; set; }
        public string? ErrorMessage { get; set; }

        // Số lượng dữ liệu
        public int UserCount { get; set; }
        public int VehicleCount { get; set; }
        public int RouteCount { get; set; }
        public int TripCount { get; set; }
        public int BookingCount { get; set; }
        public int SeatCount { get; set; }
        public int PaymentCount { get; set; }
        public int PromotionCount { get; set; }
        public int RatingCount { get; set; }

        // Dữ liệu mẫu
        public List<NguoiDung> SampleUsers { get; set; } = new List<NguoiDung>();
        public List<Xe> SampleVehicles { get; set; } = new List<Xe>();
        public List<TuyenDuong> SampleRoutes { get; set; } = new List<TuyenDuong>();
        public List<ChuyenXe> SampleTrips { get; set; } = new List<ChuyenXe>();
        public List<Ve> SampleBookings { get; set; } = new List<Ve>();
    }
}

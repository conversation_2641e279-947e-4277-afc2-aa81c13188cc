@model DatVeXe.Models.Xe
@{
    ViewData["Title"] = "Thêm xe mới";
}

<div class="container py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 fw-bold text-primary">
            <i class="bi bi-plus-circle me-3"></i>Thêm xe mới
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-action="Index">Quản trị</a></li>
                <li class="breadcrumb-item"><a asp-action="Vehicles">Quản lý xe</a></li>
                <li class="breadcrumb-item active">Thêm xe mới</li>
            </ol>
        </nav>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-truck me-2"></i>Thông tin xe
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form asp-action="CreateVehicle" method="post">
                        <div class="row g-3">
                            <!-- Biển số xe -->
                            <div class="col-md-6">
                                <label asp-for="BienSo" class="form-label fw-semibold required">
                                    <i class="bi bi-card-text text-primary me-1"></i>Biển số xe
                                </label>
                                <input asp-for="BienSo" class="form-control form-control-lg" 
                                       placeholder="VD: 29A-12345" />
                                <span asp-validation-for="BienSo" class="text-danger"></span>
                            </div>

                            <!-- Loại xe -->
                            <div class="col-md-6">
                                <label asp-for="LoaiXe" class="form-label fw-semibold required">
                                    <i class="bi bi-truck text-success me-1"></i>Loại xe
                                </label>
                                <select asp-for="LoaiXe" class="form-select form-select-lg">
                                    <option value="">-- Chọn loại xe --</option>
                                    <option value="Limousine">Limousine</option>
                                    <option value="Giường nằm">Giường nằm</option>
                                    <option value="Ghế ngồi">Ghế ngồi</option>
                                    <option value="VIP">VIP</option>
                                </select>
                                <span asp-validation-for="LoaiXe" class="text-danger"></span>
                            </div>

                            <!-- Số ghế -->
                            <div class="col-md-6">
                                <label asp-for="SoGhe" class="form-label fw-semibold required">
                                    <i class="bi bi-people text-warning me-1"></i>Số ghế
                                </label>
                                <input asp-for="SoGhe" type="number" class="form-control form-control-lg" 
                                       placeholder="VD: 45" min="1" max="60" />
                                <span asp-validation-for="SoGhe" class="text-danger"></span>
                            </div>

                            <!-- Năm sản xuất -->
                            <div class="col-md-6">
                                <label asp-for="NamSanXuat" class="form-label fw-semibold">
                                    <i class="bi bi-calendar text-info me-1"></i>Năm sản xuất
                                </label>
                                <input asp-for="NamSanXuat" type="number" class="form-control form-control-lg" 
                                       placeholder="VD: 2023" min="2000" max="@DateTime.Now.Year" />
                                <span asp-validation-for="NamSanXuat" class="text-danger"></span>
                            </div>

                            <!-- Hãng xe -->
                            <div class="col-md-6">
                                <label asp-for="HangXe" class="form-label fw-semibold">
                                    <i class="bi bi-building text-secondary me-1"></i>Hãng xe
                                </label>
                                <select asp-for="HangXe" class="form-select form-select-lg">
                                    <option value="">-- Chọn hãng xe --</option>
                                    <option value="Hyundai">Hyundai</option>
                                    <option value="Thaco">Thaco</option>
                                    <option value="Isuzu">Isuzu</option>
                                    <option value="Mercedes">Mercedes</option>
                                    <option value="Daewoo">Daewoo</option>
                                    <option value="Khác">Khác</option>
                                </select>
                                <span asp-validation-for="HangXe" class="text-danger"></span>
                            </div>

                            <!-- Màu xe -->
                            <div class="col-md-6">
                                <label asp-for="MauXe" class="form-label fw-semibold">
                                    <i class="bi bi-palette text-danger me-1"></i>Màu xe
                                </label>
                                <input asp-for="MauXe" class="form-control form-control-lg" 
                                       placeholder="VD: Trắng" />
                                <span asp-validation-for="MauXe" class="text-danger"></span>
                            </div>

                            <!-- Trạng thái hoạt động -->
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input asp-for="TrangThaiHoatDong" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="TrangThaiHoatDong" class="form-check-label fw-semibold">
                                        <i class="bi bi-toggle-on text-success me-1"></i>Xe đang hoạt động
                                    </label>
                                </div>
                            </div>

                            <!-- Ghi chú -->
                            <div class="col-12">
                                <label asp-for="GhiChu" class="form-label fw-semibold">
                                    <i class="bi bi-chat-text text-info me-1"></i>Ghi chú
                                </label>
                                <textarea asp-for="GhiChu" class="form-control" rows="3" 
                                          placeholder="Ghi chú thêm về xe (tùy chọn)"></textarea>
                                <span asp-validation-for="GhiChu" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Thông tin bổ sung -->
                        <div class="mt-4">
                            <div class="alert alert-info border-0">
                                <h6 class="alert-heading">
                                    <i class="bi bi-info-circle me-2"></i>Lưu ý
                                </h6>
                                <ul class="mb-0">
                                    <li>Biển số xe phải là duy nhất trong hệ thống</li>
                                    <li>Hệ thống sẽ tự động tạo sơ đồ ghế dựa trên số ghế và loại xe</li>
                                    <li>Xe sau khi tạo có thể được sử dụng để tạo chuyến đi</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Vehicles" class="btn btn-outline-secondary btn-lg px-4">
                                        <i class="bi bi-arrow-left me-2"></i>Quay lại
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg px-5">
                                        <i class="bi bi-plus-circle me-2"></i>Tạo xe mới
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Format license plate input
            $('#BienSo').on('input', function() {
                let value = $(this).val().toUpperCase();
                $(this).val(value);
            });

            // Auto-suggest seat count based on vehicle type
            $('#LoaiXe').change(function() {
                const vehicleType = $(this).val();
                let suggestedSeats = 0;
                
                switch(vehicleType) {
                    case 'Limousine':
                        suggestedSeats = 22;
                        break;
                    case 'Giường nằm':
                        suggestedSeats = 40;
                        break;
                    case 'Ghế ngồi':
                        suggestedSeats = 45;
                        break;
                    case 'VIP':
                        suggestedSeats = 28;
                        break;
                }
                
                if (suggestedSeats > 0 && !$('#SoGhe').val()) {
                    $('#SoGhe').val(suggestedSeats);
                }
            });

            // Form validation
            $('form').on('submit', function(e) {
                if (!$(this).valid()) {
                    e.preventDefault();
                    // Scroll to first error
                    $('html, body').animate({
                        scrollTop: $('.field-validation-error:first').offset().top - 100
                    }, 500);
                    return false;
                }
            });
        });
    </script>
}

<style>
    .form-control-lg, .form-select-lg {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control-lg:focus, .form-select-lg:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .required::after {
        content: " *";
        color: #dc3545;
    }

    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .form-check-input:checked {
        background-color: #198754;
        border-color: #198754;
    }
</style>

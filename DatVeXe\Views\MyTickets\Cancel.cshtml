@model DatVeXe.Models.CancelTicketViewModel
@{
    ViewData["Title"] = "Hủy vé - " + Model.MaVe;
}

<div class="container py-4">
    <!-- Header -->
    <div class="text-center mb-4">
        <h1 class="display-6 fw-bold text-danger">
            <i class="bi bi-x-circle me-3"></i>Hủy vé
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item"><a asp-action="Index">Vé của tôi</a></li>
                <li class="breadcrumb-item"><a asp-action="Details" asp-route-id="@Model.VeId">@Model.MaVe</a></li>
                <li class="breadcrumb-item active">Hủy vé</li>
            </ol>
        </nav>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Warning Alert -->
            <div class="alert alert-warning border-0 shadow-sm mb-4">
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle-fill fs-3 me-3"></i>
                    <div>
                        <h5 class="alert-heading mb-2">Lưu ý quan trọng</h5>
                        <p class="mb-0">Việc hủy vé sẽ không thể hoàn tác. Vui lòng đọc kỹ chính sách hủy vé trước khi xác nhận.</p>
                    </div>
                </div>
            </div>

            <!-- Trip Information -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>Thông tin chuyến đi
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-ticket-perforated text-primary me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold fs-5">@Model.MaVe</div>
                                    <small class="text-muted">Mã vé</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-calendar-event text-info me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold">@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</div>
                                    <small class="text-muted">Ngày giờ khởi hành</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="bg-light rounded p-3">
                                <div class="row align-items-center">
                                    <div class="col-md-4 text-center">
                                        <div class="fw-bold text-primary">@Model.ChuyenXe.DiemDi</div>
                                        <small class="text-muted">Điểm đi</small>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <i class="bi bi-arrow-right-circle text-primary fs-3"></i>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <div class="fw-bold text-primary">@Model.ChuyenXe.DiemDen</div>
                                        <small class="text-muted">Điểm đến</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cancellation Policy -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-shield-check me-2"></i>Chính sách hủy vé
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="text-center p-3 border rounded">
                                <i class="bi bi-cash-coin text-success fs-2 mb-2"></i>
                                <div class="fw-bold text-success fs-4">@string.Format("{0:N0}", Model.SoTienHoanLai) VNĐ</div>
                                <small class="text-muted">Số tiền hoàn lại</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center p-3 border rounded">
                                <i class="bi bi-exclamation-triangle text-warning fs-2 mb-2"></i>
                                <div class="fw-bold text-warning fs-4">@string.Format("{0:N0}", Model.PhiHuy) VNĐ</div>
                                <small class="text-muted">Phí hủy vé</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6 class="fw-bold">Quy định hủy vé:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                Hủy trước 48h: Phí hủy 5%
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-warning me-2"></i>
                                Hủy trước 24h: Phí hủy 10%
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-danger me-2"></i>
                                Hủy trước 2h: Phí hủy 20%
                            </li>
                            <li>
                                <i class="bi bi-x-circle text-danger me-2"></i>
                                Không thể hủy trong vòng 2h trước giờ khởi hành
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Cancellation Form -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-pencil-square me-2"></i>Xác nhận hủy vé
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Cancel" asp-route-id="@Model.VeId" method="post">
                        <div class="mb-3">
                            <label asp-for="LyDoHuy" class="form-label fw-semibold">
                                <i class="bi bi-chat-text me-1"></i>Lý do hủy vé <span class="text-danger">*</span>
                            </label>
                            <textarea asp-for="LyDoHuy" class="form-control" rows="4" 
                                      placeholder="Vui lòng cho biết lý do hủy vé để chúng tôi cải thiện dịch vụ..."></textarea>
                            <span asp-validation-for="LyDoHuy" class="text-danger"></span>
                        </div>

                        <div class="form-check mb-4">
                            <input asp-for="XacNhanHuy" class="form-check-input" type="checkbox" id="confirmCancel">
                            <label class="form-check-label fw-semibold" for="confirmCancel">
                                Tôi xác nhận muốn hủy vé này và đồng ý với chính sách hủy vé
                            </label>
                            <span asp-validation-for="XacNhanHuy" class="text-danger d-block"></span>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a asp-action="Details" asp-route-id="@Model.VeId" class="btn btn-outline-secondary btn-lg me-md-2">
                                <i class="bi bi-arrow-left me-2"></i>Quay lại
                            </a>
                            <button type="submit" class="btn btn-danger btn-lg" id="cancelButton" disabled>
                                <i class="bi bi-x-circle me-2"></i>Xác nhận hủy vé
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Enable/disable cancel button based on checkbox
            $('#confirmCancel').change(function() {
                $('#cancelButton').prop('disabled', !this.checked);
            });
            
            // Confirmation dialog
            $('#cancelButton').click(function(e) {
                if (!confirm('Bạn có chắc chắn muốn hủy vé này? Hành động này không thể hoàn tác.')) {
                    e.preventDefault();
                }
            });
        });
    </script>
}

<style>
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .alert {
        border-radius: 15px;
    }
    
    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        padding: 0.75rem 2rem;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
    }
    
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    
    .form-check-input:checked {
        background-color: #dc3545;
        border-color: #dc3545;
    }
</style>

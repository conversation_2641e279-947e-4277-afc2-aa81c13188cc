@model DatVeXe.Models.BookingSearchViewModel
@{
    ViewData["Title"] = "Tìm kiếm chuyến xe";
}

<div class="container py-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary mb-3">
            <i class="bi bi-search me-3"></i>Tìm kiếm chuyến xe
        </h1>
        <p class="lead text-muted">Tìm và đặt vé xe khách trực tuyến dễ dàng, nhanh chóng</p>
    </div>

    <!-- Search Form -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-funnel me-2"></i>Thông tin tìm kiếm
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form asp-action="Search" method="post">
                        <div class="row g-3">
                            <!-- Điểm đi -->
                            <div class="col-md-6">
                                <label asp-for="DiemDi" class="form-label fw-semibold">
                                    <i class="bi bi-geo-alt text-success me-1"></i>Điểm đi
                                </label>
                                <select asp-for="DiemDi" class="form-select form-select-lg">
                                    <option value="">-- Chọn điểm đi --</option>
                                    @if (ViewBag.DiemDiList != null)
                                    {
                                        @foreach (var item in ViewBag.DiemDiList)
                                        {
                                            <option value="@item">@item</option>
                                        }
                                    }
                                </select>
                                <span asp-validation-for="DiemDi" class="text-danger"></span>
                            </div>

                            <!-- Điểm đến -->
                            <div class="col-md-6">
                                <label asp-for="DiemDen" class="form-label fw-semibold">
                                    <i class="bi bi-geo-alt-fill text-danger me-1"></i>Điểm đến
                                </label>
                                <select asp-for="DiemDen" class="form-select form-select-lg">
                                    <option value="">-- Chọn điểm đến --</option>
                                    @if (ViewBag.DiemDenList != null)
                                    {
                                        @foreach (var item in ViewBag.DiemDenList)
                                        {
                                            <option value="@item">@item</option>
                                        }
                                    }
                                </select>
                                <span asp-validation-for="DiemDen" class="text-danger"></span>
                            </div>

                            <!-- Ngày khởi hành -->
                            <div class="col-md-6">
                                <label asp-for="NgayKhoiHanh" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-event text-info me-1"></i>Ngày khởi hành
                                </label>
                                <input asp-for="NgayKhoiHanh" type="date" class="form-control form-control-lg" 
                                       min="@DateTime.Now.ToString("yyyy-MM-dd")" />
                                <span asp-validation-for="NgayKhoiHanh" class="text-danger"></span>
                            </div>

                            <!-- Số hành khách -->
                            <div class="col-md-6">
                                <label asp-for="SoHanhKhach" class="form-label fw-semibold">
                                    <i class="bi bi-people text-warning me-1"></i>Số hành khách
                                </label>
                                <select asp-for="SoHanhKhach" class="form-select form-select-lg">
                                    @for (int i = 1; i <= 10; i++)
                                    {
                                        <option value="@i" selected="@(i == 1)">@i người</option>
                                    }
                                </select>
                                <span asp-validation-for="SoHanhKhach" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Bộ lọc nâng cao -->
                        <div class="mt-4">
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                                <i class="bi bi-funnel me-2"></i>Bộ lọc nâng cao
                            </button>
                        </div>

                        <div class="collapse mt-3" id="advancedFilters">
                            <div class="card border-primary">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0 text-primary">
                                        <i class="bi bi-sliders me-2"></i>Tùy chọn tìm kiếm nâng cao
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <!-- Giờ khởi hành -->
                                        <div class="col-md-6">
                                            <label asp-for="GioKhoiHanhTu" class="form-label">
                                                <i class="bi bi-clock me-1"></i>Giờ khởi hành từ
                                            </label>
                                            <input asp-for="GioKhoiHanhTu" type="time" class="form-control" />
                                        </div>
                                        <div class="col-md-6">
                                            <label asp-for="GioKhoiHanhDen" class="form-label">
                                                <i class="bi bi-clock-fill me-1"></i>Giờ khởi hành đến
                                            </label>
                                            <input asp-for="GioKhoiHanhDen" type="time" class="form-control" />
                                        </div>

                                        <!-- Loại xe -->
                                        <div class="col-md-6">
                                            <label asp-for="LoaiXe" class="form-label">
                                                <i class="bi bi-truck me-1"></i>Loại xe
                                            </label>
                                            <select asp-for="LoaiXe" class="form-select">
                                                <option value="">-- Tất cả loại xe --</option>
                                                @if (Model.DanhSachLoaiXe != null)
                                                {
                                                    @foreach (var loaiXe in Model.DanhSachLoaiXe)
                                                    {
                                                        <option value="@loaiXe">@loaiXe</option>
                                                    }
                                                }
                                            </select>
                                        </div>

                                        <!-- Nhà xe -->
                                        <div class="col-md-6">
                                            <label asp-for="NhaXe" class="form-label">
                                                <i class="bi bi-building me-1"></i>Nhà xe
                                            </label>
                                            <select asp-for="NhaXe" class="form-select">
                                                <option value="">-- Tất cả nhà xe --</option>
                                                @if (Model.DanhSachNhaXe != null)
                                                {
                                                    @foreach (var nhaXe in Model.DanhSachNhaXe)
                                                    {
                                                        <option value="@nhaXe">Nhà xe @nhaXe</option>
                                                    }
                                                }
                                            </select>
                                        </div>

                                        <!-- Giá vé -->
                                        <div class="col-md-6">
                                            <label asp-for="GiaVeTu" class="form-label">
                                                <i class="bi bi-currency-dollar me-1"></i>Giá vé từ (VNĐ)
                                            </label>
                                            <input asp-for="GiaVeTu" type="number" class="form-control" placeholder="0" min="0" step="1000" />
                                        </div>
                                        <div class="col-md-6">
                                            <label asp-for="GiaVeDen" class="form-label">
                                                <i class="bi bi-currency-dollar me-1"></i>Giá vé đến (VNĐ)
                                            </label>
                                            <input asp-for="GiaVeDen" type="number" class="form-control" placeholder="1000000" min="0" step="1000" />
                                        </div>

                                        <!-- Sắp xếp -->
                                        <div class="col-md-6">
                                            <label asp-for="SortBy" class="form-label">
                                                <i class="bi bi-sort-down me-1"></i>Sắp xếp theo
                                            </label>
                                            <select asp-for="SortBy" class="form-select">
                                                <option value="NgayKhoiHanh">Thời gian khởi hành</option>
                                                <option value="Gia">Giá vé</option>
                                                <option value="LoaiXe">Loại xe</option>
                                                <option value="GioXuatBen">Giờ xuất bến</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label asp-for="SortOrder" class="form-label">
                                                <i class="bi bi-arrow-up-down me-1"></i>Thứ tự
                                            </label>
                                            <select asp-for="SortOrder" class="form-select">
                                                <option value="asc">Tăng dần</option>
                                                <option value="desc">Giảm dần</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5 py-3">
                                    <i class="bi bi-search me-2"></i>Tìm kiếm chuyến xe
                                </button>
                                <a href="@Url.Action("Search")" class="btn btn-outline-secondary btn-lg px-4 py-3 ms-3">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Làm mới
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Results -->
    @if (Model.KetQua != null && Model.KetQua.Any())
    {
        <div class="card shadow-lg border-0">
            <div class="card-header bg-success text-white py-3">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul me-2"></i>Kết quả tìm kiếm (@Model.KetQua.Count chuyến xe)
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th class="py-3">Tuyến đường</th>
                                <th class="py-3">Thời gian</th>
                                <th class="py-3">Loại xe</th>
                                <th class="py-3">Giá vé</th>
                                <th class="py-3">Ghế trống</th>
                                <th class="py-3 text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.KetQua)
                            {
                                var soGheTrong = (item.Xe?.SoGhe ?? 0) - (item.Ves?.Count ?? 0);
                                <tr>
                                    <td class="py-3">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-arrow-right-circle text-primary me-2"></i>
                                            <div>
                                                <div class="fw-semibold">@item.DiemDiDisplay → @item.DiemDenDisplay</div>
                                                <small class="text-muted">
                                                    <i class="bi bi-geo me-1"></i>@(item.TuyenDuong?.KhoangCach ?? 0) km
                                                    @if (item.TuyenDuong?.ThoiGianDuKien != null)
                                                    {
                                                        <span class="ms-2">
                                                            <i class="bi bi-clock me-1"></i>~@item.TuyenDuong.ThoiGianDuKien.ToString(@"hh\:mm")
                                                        </span>
                                                    }
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-3">
                                        <div>
                                            <div class="fw-semibold">@item.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                                            <div class="text-primary fw-bold">@item.NgayKhoiHanh.ToString("HH:mm")</div>
                                            <small class="text-muted">Xuất bến</small>
                                        </div>
                                    </td>
                                    <td class="py-3">
                                        <span class="badge bg-info mb-1">@item.Xe?.LoaiXe</span>
                                        <br>
                                        <small class="text-muted">
                                            <i class="bi bi-truck me-1"></i>@item.Xe?.BienSo
                                        </small>
                                        <br>
                                        <small class="text-success">
                                            <i class="bi bi-people me-1"></i>@(item.Xe?.SoGhe ?? 0) chỗ
                                        </small>
                                    </td>
                                    <td class="py-3">
                                        <div class="fw-bold text-success fs-5">@string.Format("{0:N0}", item.Gia) VNĐ</div>
                                    </td>
                                    <td class="py-3">
                                        <span class="badge @(soGheTrong > 5 ? "bg-success" : soGheTrong > 0 ? "bg-warning" : "bg-danger")">
                                            @soGheTrong/@(item.Xe?.SoGhe ?? 0) ghế
                                        </span>
                                    </td>
                                    <td class="py-3 text-center">
                                        @if (soGheTrong >= Model.SoHanhKhach)
                                        {
                                            <a asp-action="SelectTrip" asp-route-id="@item.ChuyenXeId" 
                                               class="btn btn-primary btn-sm px-3">
                                                <i class="bi bi-ticket-perforated me-1"></i>Đặt vé
                                            </a>
                                        }
                                        else
                                        {
                                            <button class="btn btn-secondary btn-sm" disabled>
                                                <i class="bi bi-x-circle me-1"></i>Hết chỗ
                                            </button>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
    else if (Model.KetQua != null)
    {
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="bi bi-search display-1 text-muted"></i>
            </div>
            <h4 class="text-muted">Không tìm thấy chuyến xe phù hợp</h4>
            <p class="text-muted">Vui lòng thử lại với điều kiện tìm kiếm khác</p>
        </div>
    }
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Set default date to today
            if (!$('#NgayKhoiHanh').val()) {
                $('#NgayKhoiHanh').val(new Date().toISOString().split('T')[0]);
            }
            
            // Auto-submit form when date changes (optional)
            $('#NgayKhoiHanh').change(function() {
                if ($('#DiemDi').val() && $('#DiemDen').val()) {
                    // Auto search when all required fields are filled
                    // $('form').submit();
                }
            });
        });
    </script>
}

<style>
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .form-select-lg, .form-control-lg {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-select-lg:focus, .form-control-lg:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    
    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .badge {
        font-size: 0.75em;
        padding: 0.5em 0.75em;
    }
</style>

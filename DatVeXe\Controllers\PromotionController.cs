using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;
using DatVeXe.Attributes;

namespace DatVeXe.Controllers
{
    public class PromotionController : Controller
    {
        private readonly DatVeXeContext _context;
        private readonly ILogger<PromotionController> _logger;

        public PromotionController(DatVeXeContext context, ILogger<PromotionController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: Promotion - Danh sách khuyến mãi công khai
        public async Task<IActionResult> Index()
        {
            var now = DateTime.Now;
            var khuyenMais = await _context.KhuyenMais
                .Where(k => k.<PERSON>rang<PERSON>ong && 
                           k.<PERSON>ay<PERSON>at<PERSON>au <= now && 
                           k.NgayKetThuc >= now &&
                           (k.SoLuong == null || k.SoLuongDaSuDung < k.SoLuong))
                .OrderByDescending(k => k.<PERSON>ay<PERSON>)
                .ToListAsync();

            return View(khuyenMais);
        }

        // API: Kiể<PERSON> tra mã khuyến mãi
        [HttpPost]
        public async Task<IActionResult> CheckPromoCode(string promoCode, decimal orderValue)
        {
            try
            {
                if (string.IsNullOrEmpty(promoCode))
                {
                    return Json(new { success = false, message = "Vui lòng nhập mã khuyến mãi" });
                }

                var now = DateTime.Now;
                var khuyenMai = await _context.KhuyenMais
                    .FirstOrDefaultAsync(k => k.MaKhuyenMai == promoCode.ToUpper() &&
                                             k.TrangThaiHoatDong &&
                                             k.NgayBatDau <= now &&
                                             k.NgayKetThuc >= now);

                if (khuyenMai == null)
                {
                    return Json(new { success = false, message = "Mã khuyến mãi không tồn tại hoặc đã hết hạn" });
                }

                // Kiểm tra số lượng
                if (khuyenMai.SoLuong.HasValue && khuyenMai.SoLuongDaSuDung >= khuyenMai.SoLuong)
                {
                    return Json(new { success = false, message = "Mã khuyến mãi đã hết lượt sử dụng" });
                }

                // Kiểm tra giá trị đơn hàng tối thiểu
                if (khuyenMai.GiaTriDonHangToiThieu.HasValue && orderValue < khuyenMai.GiaTriDonHangToiThieu)
                {
                    return Json(new { 
                        success = false, 
                        message = $"Đơn hàng tối thiểu {khuyenMai.GiaTriDonHangToiThieu:N0} VNĐ để sử dụng mã này" 
                    });
                }

                // Kiểm tra khách hàng mới
                if (khuyenMai.ApDungChoKhachHangMoi)
                {
                    int? userId = HttpContext.Session.GetInt32("UserId");
                    if (userId.HasValue)
                    {
                        var hasBookedBefore = await _context.Ves
                            .AnyAsync(v => v.NguoiDungId == userId.Value && v.TrangThai == TrangThaiVe.DaDat);
                        
                        if (hasBookedBefore)
                        {
                            return Json(new { success = false, message = "Mã khuyến mãi chỉ dành cho khách hàng mới" });
                        }
                    }
                }

                // Tính số tiền giảm
                var soTienGiam = orderValue * khuyenMai.PhanTramGiam / 100;
                if (khuyenMai.SoTienGiamToiDa.HasValue && soTienGiam > khuyenMai.SoTienGiamToiDa)
                {
                    soTienGiam = khuyenMai.SoTienGiamToiDa.Value;
                }

                return Json(new { 
                    success = true, 
                    message = "Áp dụng mã khuyến mãi thành công",
                    khuyenMaiId = khuyenMai.KhuyenMaiId,
                    tenKhuyenMai = khuyenMai.TenKhuyenMai,
                    phanTramGiam = khuyenMai.PhanTramGiam,
                    soTienGiam = soTienGiam,
                    soTienPhaiTra = orderValue - soTienGiam
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi kiểm tra mã khuyến mãi {PromoCode}", promoCode);
                return Json(new { success = false, message = "Có lỗi xảy ra khi kiểm tra mã khuyến mãi" });
            }
        }

        // API: Áp dụng khuyến mãi vào booking
        [HttpPost]
        public async Task<IActionResult> ApplyPromotion(string sessionId, string promoCode)
        {
            try
            {
                // Lấy thông tin booking từ session
                var sessionData = HttpContext.Session.GetString($"Booking_{sessionId}");
                if (string.IsNullOrEmpty(sessionData))
                {
                    return Json(new { success = false, message = "Phiên đặt vé không hợp lệ" });
                }

                var bookingStep = System.Text.Json.JsonSerializer.Deserialize<BookingStepViewModel>(sessionData);
                if (bookingStep == null || !bookingStep.ChuyenXeId.HasValue)
                {
                    return Json(new { success = false, message = "Thông tin đặt vé không hợp lệ" });
                }

                // Lấy thông tin chuyến xe
                var chuyenXe = await _context.ChuyenXes
                    .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

                if (chuyenXe == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy chuyến xe" });
                }

                // Kiểm tra mã khuyến mãi
                var checkResult = await CheckPromoCode(promoCode, chuyenXe.Gia);
                var checkData = System.Text.Json.JsonSerializer.Deserialize<dynamic>(checkResult.ToString());
                
                // Cập nhật booking step với thông tin khuyến mãi
                if (checkData.success)
                {
                    bookingStep.KhuyenMaiId = checkData.khuyenMaiId;
                    bookingStep.MaKhuyenMai = promoCode;
                    bookingStep.SoTienGiam = checkData.soTienGiam;
                    bookingStep.TongTien = checkData.soTienPhaiTra;

                    // Lưu lại vào session
                    HttpContext.Session.SetString($"Booking_{sessionId}", System.Text.Json.JsonSerializer.Serialize(bookingStep));
                }

                return checkResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi áp dụng khuyến mãi cho session {SessionId}", sessionId);
                return Json(new { success = false, message = "Có lỗi xảy ra khi áp dụng khuyến mãi" });
            }
        }

        // API: Hủy khuyến mãi
        [HttpPost]
        public async Task<IActionResult> RemovePromotion(string sessionId)
        {
            try
            {
                var sessionData = HttpContext.Session.GetString($"Booking_{sessionId}");
                if (string.IsNullOrEmpty(sessionData))
                {
                    return Json(new { success = false, message = "Phiên đặt vé không hợp lệ" });
                }

                var bookingStep = System.Text.Json.JsonSerializer.Deserialize<BookingStepViewModel>(sessionData);
                if (bookingStep == null || !bookingStep.ChuyenXeId.HasValue)
                {
                    return Json(new { success = false, message = "Thông tin đặt vé không hợp lệ" });
                }

                // Lấy giá gốc
                var chuyenXe = await _context.ChuyenXes
                    .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

                if (chuyenXe == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy chuyến xe" });
                }

                // Reset thông tin khuyến mãi
                bookingStep.KhuyenMaiId = null;
                bookingStep.MaKhuyenMai = null;
                bookingStep.SoTienGiam = 0;
                bookingStep.TongTien = chuyenXe.Gia;

                // Lưu lại vào session
                HttpContext.Session.SetString($"Booking_{sessionId}", System.Text.Json.JsonSerializer.Serialize(bookingStep));

                return Json(new { 
                    success = true, 
                    message = "Đã hủy khuyến mãi",
                    tongTien = chuyenXe.Gia
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi hủy khuyến mãi cho session {SessionId}", sessionId);
                return Json(new { success = false, message = "Có lỗi xảy ra khi hủy khuyến mãi" });
            }
        }

        // Helper method để cập nhật số lượng sử dụng khuyến mãi
        public async Task<bool> UpdatePromotionUsageAsync(int khuyenMaiId)
        {
            try
            {
                var khuyenMai = await _context.KhuyenMais.FindAsync(khuyenMaiId);
                if (khuyenMai != null)
                {
                    khuyenMai.SoLuongDaSuDung++;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi cập nhật số lượng sử dụng khuyến mãi {KhuyenMaiId}", khuyenMaiId);
                return false;
            }
        }
    }
}

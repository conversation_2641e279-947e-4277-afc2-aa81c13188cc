using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;

namespace DatVeXe.Controllers
{
    public class AdminController : Controller
    {
        private readonly DatVeXeContext _context;
        private readonly ILogger<AdminController> _logger;

        public AdminController(DatVeXeContext context, ILogger<AdminController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: Admin - Dashboard
        public async Task<IActionResult> Index()
        {
            var stats = new AdminDashboardViewModel
            {
                TotalUsers = await _context.NguoiDungs.CountAsync(),
                TotalVehicles = await _context.Xes.CountAsync(),
                TotalRoutes = await _context.TuyenDuongs.CountAsync(),
                TotalTrips = await _context.ChuyenXes.CountAsync(),
                TotalBookings = await _context.Ves.CountAsync(),
                TotalRevenue = await _context.Ves
                    .Where(v => v.TrangThai == TrangThaiVe.DaDat)
                    .SumAsync(v => v.GiaVe - v.SoTien<PERSON>),
                RecentBookings = await _context.Ves
                    .Include(v => v.ChuyenXe)
                    .Include(v => v.NguoiDung)
                    .OrderByDescending(v => v.NgayDat)
                    .Take(10)
                    .ToListAsync()
            };

            return View(stats);
        }

        // GET: Admin/Vehicles - Quản lý xe
        public async Task<IActionResult> Vehicles()
        {
            var vehicles = await _context.Xes
                .OrderBy(x => x.BienSo)
                .ToListAsync();
            return View(vehicles);
        }

        // GET: Admin/Routes - Quản lý tuyến đường
        public async Task<IActionResult> Routes()
        {
            var routes = await _context.TuyenDuongs
                .OrderBy(t => t.DiemDi)
                .ThenBy(t => t.DiemDen)
                .ToListAsync();
            return View(routes);
        }

        // GET: Admin/Trips - Quản lý chuyến xe
        public async Task<IActionResult> Trips()
        {
            var trips = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .OrderByDescending(c => c.NgayKhoiHanh)
                .ToListAsync();
            return View(trips);
        }

        // GET: Admin/Bookings - Quản lý đặt vé
        public async Task<IActionResult> Bookings()
        {
            var bookings = await _context.Ves
                .Include(v => v.ChuyenXe)
                .Include(v => v.NguoiDung)
                .Include(v => v.ChoNgoi)
                .OrderByDescending(v => v.NgayDat)
                .ToListAsync();
            return View(bookings);
        }

        // GET: Admin/Promotions - Quản lý khuyến mãi
        public async Task<IActionResult> Promotions()
        {
            var promotions = await _context.KhuyenMais
                .OrderByDescending(k => k.NgayTao)
                .ToListAsync();
            return View(promotions);
        }

        // GET: Admin/CreateVehicle - Tạo xe mới
        public IActionResult CreateVehicle()
        {
            return View(new Xe());
        }

        // POST: Admin/CreateVehicle
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateVehicle(Xe xe)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    _context.Xes.Add(xe);
                    await _context.SaveChangesAsync();

                    // Tạo ghế cho xe mới
                    await CreateSeatsForVehicle(xe.XeId, xe.LoaiXe, xe.SoGhe);

                    TempData["Success"] = $"Đã tạo xe {xe.BienSo} thành công!";
                    return RedirectToAction("Vehicles");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi tạo xe mới");
                    TempData["Error"] = "Có lỗi xảy ra khi tạo xe mới";
                }
            }
            return View(xe);
        }

        // GET: Admin/CreateRoute - Tạo tuyến đường mới
        public IActionResult CreateRoute()
        {
            return View(new TuyenDuong());
        }

        // POST: Admin/CreateRoute
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateRoute(TuyenDuong tuyenDuong)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    _context.TuyenDuongs.Add(tuyenDuong);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = $"Đã tạo tuyến đường {tuyenDuong.DiemDi} - {tuyenDuong.DiemDen} thành công!";
                    return RedirectToAction("Routes");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi tạo tuyến đường mới");
                    TempData["Error"] = "Có lỗi xảy ra khi tạo tuyến đường mới";
                }
            }
            return View(tuyenDuong);
        }

        // GET: Admin/CreateTrip - Tạo chuyến xe mới
        public async Task<IActionResult> CreateTrip()
        {
            ViewBag.Vehicles = await _context.Xes.Where(x => x.TrangThaiHoatDong).ToListAsync();
            ViewBag.Routes = await _context.TuyenDuongs.Where(t => t.TrangThaiHoatDong).ToListAsync();
            return View(new ChuyenXe());
        }

        // POST: Admin/CreateTrip
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateTrip(ChuyenXe chuyenXe)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    _context.ChuyenXes.Add(chuyenXe);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Đã tạo chuyến xe mới thành công!";
                    return RedirectToAction("Trips");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi tạo chuyến xe mới");
                    TempData["Error"] = "Có lỗi xảy ra khi tạo chuyến xe mới";
                }
            }

            ViewBag.Vehicles = await _context.Xes.Where(x => x.TrangThaiHoatDong).ToListAsync();
            ViewBag.Routes = await _context.TuyenDuongs.Where(t => t.TrangThaiHoatDong).ToListAsync();
            return View(chuyenXe);
        }

        // GET: Admin/CreatePromotion - Tạo khuyến mãi mới
        public IActionResult CreatePromotion()
        {
            return View(new KhuyenMai());
        }

        // POST: Admin/CreatePromotion
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreatePromotion(KhuyenMai khuyenMai)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    khuyenMai.NgayTao = DateTime.Now;
                    _context.KhuyenMais.Add(khuyenMai);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = $"Đã tạo khuyến mãi {khuyenMai.TenKhuyenMai} thành công!";
                    return RedirectToAction("Promotions");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi tạo khuyến mãi mới");
                    TempData["Error"] = "Có lỗi xảy ra khi tạo khuyến mãi mới";
                }
            }
            return View(khuyenMai);
        }

        // Helper method để tạo ghế cho xe
        private async Task CreateSeatsForVehicle(int xeId, string loaiXe, int soGhe)
        {
            var seats = new List<ChoNgoi>();

            for (int i = 1; i <= soGhe; i++)
            {
                var seat = new ChoNgoi
                {
                    XeId = xeId,
                    SoGhe = i.ToString(),
                    LoaiGhe = loaiXe,
                    TrangThai = TrangThaiChoNgoi.Trong
                };

                seats.Add(seat);
            }

            _context.ChoNgois.AddRange(seats);
            await _context.SaveChangesAsync();
        }

        // API: Xóa xe
        [HttpPost]
        public async Task<IActionResult> DeleteVehicle(int id)
        {
            try
            {
                var vehicle = await _context.Xes.FindAsync(id);
                if (vehicle != null)
                {
                    // Kiểm tra xem xe có chuyến đi nào không
                    var hasTrips = await _context.ChuyenXes.AnyAsync(c => c.XeId == id);
                    if (hasTrips)
                    {
                        return Json(new { success = false, message = "Không thể xóa xe đã có chuyến đi" });
                    }

                    _context.Xes.Remove(vehicle);
                    await _context.SaveChangesAsync();
                    return Json(new { success = true, message = "Đã xóa xe thành công" });
                }
                return Json(new { success = false, message = "Không tìm thấy xe" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xóa xe {VehicleId}", id);
                return Json(new { success = false, message = "Có lỗi xảy ra khi xóa xe" });
            }
        }

        // API: Xóa tuyến đường
        [HttpPost]
        public async Task<IActionResult> DeleteRoute(int id)
        {
            try
            {
                var route = await _context.TuyenDuongs.FindAsync(id);
                if (route != null)
                {
                    // Kiểm tra xem tuyến đường có chuyến đi nào không
                    var hasTrips = await _context.ChuyenXes.AnyAsync(c => c.TuyenDuongId == id);
                    if (hasTrips)
                    {
                        return Json(new { success = false, message = "Không thể xóa tuyến đường đã có chuyến đi" });
                    }

                    _context.TuyenDuongs.Remove(route);
                    await _context.SaveChangesAsync();
                    return Json(new { success = true, message = "Đã xóa tuyến đường thành công" });
                }
                return Json(new { success = false, message = "Không tìm thấy tuyến đường" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xóa tuyến đường {RouteId}", id);
                return Json(new { success = false, message = "Có lỗi xảy ra khi xóa tuyến đường" });
            }
        }
    }

    // ViewModel cho Admin Dashboard
    public class AdminDashboardViewModel
    {
        public int TotalUsers { get; set; }
        public int TotalVehicles { get; set; }
        public int TotalRoutes { get; set; }
        public int TotalTrips { get; set; }
        public int TotalBookings { get; set; }
        public decimal TotalRevenue { get; set; }
        public List<Ve> RecentBookings { get; set; } = new List<Ve>();
    }
}

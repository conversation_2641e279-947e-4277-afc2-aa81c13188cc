using System.ComponentModel.DataAnnotations;

namespace DatVeXe.Models
{
    // ViewModels cho quy trình đặt vé hoàn chỉnh
    public class BookingStepViewModel
    {
        public int CurrentStep { get; set; } = 1;
        public int TotalSteps { get; set; } = 4;
        public string SessionId { get; set; } = string.Empty;
        
        // Step 1: Chọn chuyến xe
        public int? ChuyenXeId { get; set; }
        public ChuyenXe? ChuyenXe { get; set; }
        
        // Step 2: Chọn ghế
        public int? ChoNgoiId { get; set; }
        public ChoNgoi? ChoNgoi { get; set; }
        
        // Step 3: Thông tin hành khách
        [Required(ErrorMessage = "Vui lòng nhập tên khách hàng")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Tên khách hàng phải từ 2 đến 100 ký tự")]
        [Display(Name = "Tên khách hàng")]
        public string TenKhach { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập số điện thoại")]
        [RegularExpression(@"^(0[0-9]{9})$", ErrorMessage = "Số điện thoại không hợp lệ (phải bắt đầu bằng số 0 và có 10 số)")]
        [Display(Name = "Số điện thoại")]
        public string SoDienThoai { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [Display(Name = "Email")]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "Ghi chú")]
        public string? GhiChu { get; set; }
        
        // Step 4: Thanh toán
        [Required(ErrorMessage = "Vui lòng chọn phương thức thanh toán")]
        [Display(Name = "Phương thức thanh toán")]
        public PhuongThucThanhToan PhuongThucThanhToan { get; set; }
        
        public decimal TongTien { get; set; }
        
        // Kết quả
        public string? MaVe { get; set; }
        public string? MaGiaoDich { get; set; }

        // Validation methods
        public bool IsStep1Valid()
        {
            return ChuyenXeId.HasValue;
        }

        public bool IsStep2Valid()
        {
            return IsStep1Valid() && ChoNgoiId.HasValue;
        }

        public bool IsStep3Valid()
        {
            return IsStep2Valid() &&
                   !string.IsNullOrWhiteSpace(TenKhach) &&
                   !string.IsNullOrWhiteSpace(SoDienThoai);
        }

        public bool IsStep4Valid()
        {
            return IsStep3Valid() && PhuongThucThanhToan != 0;
        }
    }

    public class BookingConfirmationViewModel
    {
        public string MaVe { get; set; } = string.Empty;
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string? Email { get; set; }
        public decimal GiaVe { get; set; }
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public ChoNgoi? ChoNgoi { get; set; }
        public ThanhToan? ThanhToan { get; set; }
        public string QRCodeData { get; set; } = string.Empty;
        public bool EmailSent { get; set; }
        public bool SMSSent { get; set; }
        public string BookingReference { get; set; } = string.Empty;
    }

    public class PaymentRequestViewModel
    {
        [Required]
        public int VeId { get; set; }
        
        [Required]
        [Display(Name = "Phương thức thanh toán")]
        public PhuongThucThanhToan PhuongThuc { get; set; }
        
        [Required]
        [Display(Name = "Số tiền")]
        public decimal SoTien { get; set; }
        
        public string? ReturnUrl { get; set; }
        public string? CancelUrl { get; set; }
        public string? OrderInfo { get; set; }
    }

    public class PaymentResponseViewModel
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? MaGiaoDich { get; set; }
        public string? PaymentUrl { get; set; }
        public TrangThaiThanhToan TrangThai { get; set; }
        public DateTime? ThoiGianThanhToan { get; set; }
        public string? ErrorCode { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class BookingSearchViewModel
    {
        [Display(Name = "Điểm đi")]
        public string? DiemDi { get; set; }

        [Display(Name = "Điểm đến")]
        public string? DiemDen { get; set; }

        [Display(Name = "Ngày khởi hành")]
        [DataType(DataType.Date)]
        public DateTime? NgayKhoiHanh { get; set; }

        [Display(Name = "Số hành khách")]
        [Range(1, 10, ErrorMessage = "Số hành khách phải từ 1 đến 10")]
        public int SoHanhKhach { get; set; } = 1;

        public List<ChuyenXe> KetQua { get; set; } = new List<ChuyenXe>();
    }

    public class SeatSelectionViewModel
    {
        public int ChuyenXeId { get; set; }
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public Xe Xe { get; set; } = new Xe();
        public List<ChoNgoiViewModel> DanhSachGhe { get; set; } = new List<ChoNgoiViewModel>();
        public int? SelectedSeatId { get; set; }
        public string SessionId { get; set; } = string.Empty;
        
        // Thông tin layout ghế
        public int SoHang { get; set; }
        public int SoCot { get; set; }
        public string LoaiXe { get; set; } = string.Empty;
    }

    public class PassengerInfoViewModel
    {
        public int ChuyenXeId { get; set; }
        public int ChoNgoiId { get; set; }
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public ChoNgoi ChoNgoi { get; set; } = new ChoNgoi();

        [Required(ErrorMessage = "Vui lòng nhập tên khách hàng")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Tên khách hàng phải từ 2 đến 100 ký tự")]
        [Display(Name = "Tên khách hàng")]
        public string TenKhach { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập số điện thoại")]
        [RegularExpression(@"^(0[0-9]{9})$", ErrorMessage = "Số điện thoại không hợp lệ")]
        [Display(Name = "Số điện thoại")]
        public string SoDienThoai { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [Display(Name = "Email")]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "Ghi chú")]
        public string? GhiChu { get; set; }

        [Display(Name = "Nhận thông báo qua SMS")]
        public bool NhanSMS { get; set; } = true;

        [Display(Name = "Nhận thông báo qua Email")]
        public bool NhanEmail { get; set; } = true;
    }

    public class PaymentSelectionViewModel
    {
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? GhiChu { get; set; }
        public decimal GiaVe { get; set; }
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public ChoNgoi ChoNgoi { get; set; } = new ChoNgoi();

        [Required(ErrorMessage = "Vui lòng chọn phương thức thanh toán")]
        [Display(Name = "Phương thức thanh toán")]
        public PhuongThucThanhToan PhuongThucThanhToan { get; set; }

        public decimal TongTien { get; set; }
        public decimal PhiDichVu { get; set; } = 0;
        public decimal ThanhTien { get; set; }

        // Thông tin khuyến mãi
        public string? MaKhuyenMai { get; set; }
        public decimal GiamGia { get; set; } = 0;

        // Điều khoản
        [Required(ErrorMessage = "Vui lòng đồng ý với điều khoản sử dụng")]
        [Display(Name = "Tôi đồng ý với điều khoản sử dụng")]
        public bool DongYDieuKhoan { get; set; }
    }

    public class BookingSummaryViewModel
    {
        public string MaVe { get; set; } = string.Empty;
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public ChoNgoi ChoNgoi { get; set; } = new ChoNgoi();
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string? Email { get; set; }
        public decimal TongTien { get; set; }
        public PhuongThucThanhToan PhuongThucThanhToan { get; set; }
        public TrangThaiThanhToan TrangThaiThanhToan { get; set; }
        public DateTime NgayDat { get; set; }
        public string? GhiChu { get; set; }
    }
}

@model DatVeXe.Models.TicketDetailViewModel
@{
    ViewData["Title"] = "Chi tiết vé - " + Model.Ve.MaVe;
    var now = DateTime.Now;
    var isUpcoming = Model.ChuyenXe.NgayKhoiHanh > now;
    var isCompleted = Model.ChuyenXe.NgayKhoiHanh <= now && Model.Ve.TrangThai == DatVeXe.Models.TrangThaiVe.DaDat;
    var isCancelled = Model.Ve.TrangThai == DatVeXe.Models.TrangThaiVe.DaHuy;
}

<div class="container py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="display-6 fw-bold text-primary mb-2">Chi tiết vé</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a asp-controller="MyTickets" asp-action="Index">Vé của tôi</a></li>
                    <li class="breadcrumb-item active">@Model.Ve.MaVe</li>
                </ol>
            </nav>
        </div>
        <div class="text-end">
            @{
                var statusClass = isCancelled ? "bg-danger" : isCompleted ? "bg-success" : "bg-primary";
                var statusText = isCancelled ? "Đã hủy" : isCompleted ? "Hoàn thành" : "Đã đặt";
            }
            <span class="badge @statusClass fs-6 px-3 py-2">@statusText</span>
        </div>
    </div>

    <div class="row g-4">
        <!-- Ticket Information -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-ticket-perforated me-2"></i>Thông tin vé
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Ticket Code and Date -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-qr-code text-primary me-3 fs-4"></i>
                                <div>
                                    <h4 class="fw-bold text-primary mb-1">@Model.Ve.MaVe</h4>
                                    <small class="text-muted">Mã vé</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-calendar-check text-success me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold">@Model.Ve.NgayDat.ToString("dd/MM/yyyy HH:mm")</div>
                                    <small class="text-muted">Ngày đặt vé</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Route Information -->
                    <div class="bg-light rounded p-4 mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-4 text-center">
                                <div class="fw-bold fs-4 text-primary">@Model.ChuyenXe.DiemDi</div>
                                <small class="text-muted">Điểm đi</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="bi bi-arrow-right-circle text-primary fs-1"></i>
                                <div class="mt-2">
                                    <small class="text-muted">@(Model.ChuyenXe.TuyenDuong?.KhoangCach ?? 0) km</small>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="fw-bold fs-4 text-primary">@Model.ChuyenXe.DiemDen</div>
                                <small class="text-muted">Điểm đến</small>
                            </div>
                        </div>
                    </div>

                    <!-- Trip Details -->
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <i class="bi bi-calendar-event text-info me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold">@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                                    <div class="text-primary">@Model.ChuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                                    <small class="text-muted">Ngày và giờ khởi hành</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <i class="bi bi-geo-alt text-warning me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold">Ghế số @Model.ChoNgoi.SoGhe</div>
                                    <div class="text-primary">@Model.ChoNgoi.LoaiGhe</div>
                                    <small class="text-muted">Vị trí ghế</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <i class="bi bi-truck text-success me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold">@Model.ChuyenXe.Xe.LoaiXe</div>
                                    <div class="text-primary">@Model.ChuyenXe.Xe.BienSo</div>
                                    <small class="text-muted">Thông tin xe</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <i class="bi bi-person text-primary me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold">@Model.Ve.TenKhach</div>
                                    <div class="text-primary">@Model.Ve.SoDienThoai</div>
                                    <small class="text-muted">Thông tin hành khách</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            @if (Model.ThanhToan != null)
            {
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-credit-card me-2"></i>Thông tin thanh toán
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-hash text-primary me-2"></i>
                                    <div>
                                        <div class="fw-bold">@Model.ThanhToan.MaGiaoDich</div>
                                        <small class="text-muted">Mã giao dịch</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-wallet text-success me-2"></i>
                                    <div>
                                        <div class="fw-bold">@Model.ThanhToan.PhuongThuc</div>
                                        <small class="text-muted">Phương thức</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-clock text-info me-2"></i>
                                    <div>
                                        <div class="fw-bold">@Model.ThanhToan.ThoiGianTao.ToString("dd/MM/yyyy HH:mm")</div>
                                        <small class="text-muted">Thời gian thanh toán</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <div>
                                        <div class="fw-bold">@Model.ThanhToan.TrangThai</div>
                                        <small class="text-muted">Trạng thái</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Rating -->
            @if (Model.DanhGia != null)
            {
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-star-fill me-2"></i>Đánh giá của bạn
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="text-center">
                                    <div class="display-6 text-warning">@Model.DanhGia.DiemDanhGia</div>
                                    <div class="text-warning">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="bi @(i <= Model.DanhGia.DiemDanhGia ? "bi-star-fill" : "bi-star")"></i>
                                        }
                                    </div>
                                    <small class="text-muted">Điểm tổng thể</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <small class="text-muted">Chất lượng xe:</small>
                                        <div class="text-warning">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="bi @(i <= Model.DanhGia.ChatLuongXe ? "bi-star-fill" : "bi-star") small"></i>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Thái độ tài xế:</small>
                                        <div class="text-warning">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="bi @(i <= Model.DanhGia.ThaiDoTaiXe ? "bi-star-fill" : "bi-star") small"></i>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Đúng giờ:</small>
                                        <div class="text-warning">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="bi @(i <= Model.DanhGia.DungGio ? "bi-star-fill" : "bi-star") small"></i>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Giá cả:</small>
                                        <div class="text-warning">
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="bi @(i <= Model.DanhGia.GiaCa ? "bi-star-fill" : "bi-star") small"></i>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(Model.DanhGia.NhanXet))
                        {
                            <div class="mt-3">
                                <strong>Nhận xét:</strong>
                                <p class="mt-2 p-3 bg-light rounded">@Model.DanhGia.NhanXet</p>
                            </div>
                        }
                        <small class="text-muted">Đánh giá ngày @Model.DanhGia.NgayDanhGia.ToString("dd/MM/yyyy")</small>
                    </div>
                </div>
            }
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- QR Code -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-qr-code me-2"></i>Mã QR vé
                    </h6>
                </div>
                <div class="card-body text-center">
                    @if (!string.IsNullOrEmpty(Model.QRCodeData))
                    {
                        <img src="@Model.QRCodeData" alt="QR Code" class="img-fluid mb-3" style="max-width: 200px;" />
                        <p class="small text-muted">Xuất trình mã QR này khi lên xe</p>
                    }
                    else
                    {
                        <div class="py-4">
                            <i class="bi bi-qr-code display-4 text-muted"></i>
                            <p class="text-muted mt-2">Không thể tạo mã QR</p>
                        </div>
                    }
                </div>
            </div>

            <!-- Price Breakdown -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-receipt me-2"></i>Chi tiết giá
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Giá vé:</span>
                        <span class="fw-bold">@string.Format("{0:N0}", Model.Ve.GiaVe) VNĐ</span>
                    </div>
                    @if (Model.Ve.SoTienGiam > 0)
                    {
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>Giảm giá:</span>
                            <span class="fw-bold">-@string.Format("{0:N0}", Model.Ve.SoTienGiam) VNĐ</span>
                        </div>
                        @if (Model.Ve.KhuyenMai != null)
                        {
                            <small class="text-muted">(@Model.Ve.KhuyenMai.TenKhuyenMai)</small>
                        }
                    }
                    <hr>
                    <div class="d-flex justify-content-between">
                        <span class="fw-bold">Tổng cộng:</span>
                        <span class="fw-bold text-primary fs-5">@string.Format("{0:N0}", Model.Ve.GiaVe - Model.Ve.SoTienGiam) VNĐ</span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-dark text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-gear me-2"></i>Thao tác
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if (Model.CanCancel)
                        {
                            <a asp-action="Cancel" asp-route-id="@Model.Ve.VeId" class="btn btn-outline-danger">
                                <i class="bi bi-x-circle me-2"></i>Hủy vé
                            </a>
                        }
                        
                        @if (Model.CanRate)
                        {
                            <a asp-action="Rate" asp-route-id="@Model.Ve.VeId" class="btn btn-outline-warning">
                                <i class="bi bi-star me-2"></i>Đánh giá chuyến đi
                            </a>
                        }
                        
                        <button onclick="window.print()" class="btn btn-outline-primary">
                            <i class="bi bi-printer me-2"></i>In vé
                        </button>
                        
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Print styles
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');
        });
        
        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');
        });
    </script>
}

<style>
    @@media print {
        .btn, .card-header, nav, .sidebar { display: none !important; }
        .card { border: 1px solid #000 !important; box-shadow: none !important; }
        .container { max-width: 100% !important; }
    }
    
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
</style>

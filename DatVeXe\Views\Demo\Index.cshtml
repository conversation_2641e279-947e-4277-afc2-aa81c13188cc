@model DatVeXe.Controllers.DemoViewModel
@{
    ViewData["Title"] = "Demo Hệ thống Đặt vé xe";
}

<div class="container py-4">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary mb-3">
            <i class="bi bi-play-circle me-3"></i>Demo Hệ thống Đặt vé xe
        </h1>
        <p class="lead text-muted">Test toàn bộ quy trình đặt vé từ tìm kiếm đến thanh toán</p>
    </div>

    <!-- Alert Messages -->
    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Statistics -->
    <div class="row g-4 mb-5">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="bi bi-truck display-4 text-primary mb-3"></i>
                    <h3 class="fw-bold">@Model.TotalVehicles</h3>
                    <p class="text-muted mb-0">Xe</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="bi bi-geo-alt display-4 text-success mb-3"></i>
                    <h3 class="fw-bold">@Model.TotalRoutes</h3>
                    <p class="text-muted mb-0">Tuyến đường</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="bi bi-calendar-event display-4 text-warning mb-3"></i>
                    <h3 class="fw-bold">@Model.TotalTrips</h3>
                    <p class="text-muted mb-0">Chuyến xe</p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="bi bi-square display-4 text-info mb-3"></i>
                    <h3 class="fw-bold">@Model.TotalSeats</h3>
                    <p class="text-muted mb-0">Ghế</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning me-2"></i>Thao tác nhanh
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a asp-action="CreateFullData" class="btn btn-success w-100 py-3" 
                               onclick="return confirm('Tạo dữ liệu demo đầy đủ? Thao tác này sẽ xóa dữ liệu cũ.')">
                                <i class="bi bi-database-add me-2"></i>
                                <div>Tạo dữ liệu demo</div>
                                <small class="d-block">Xe, tuyến đường, chuyến xe</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a asp-action="TestBooking" class="btn btn-primary w-100 py-3">
                                <i class="bi bi-ticket-perforated me-2"></i>
                                <div>Test đặt vé</div>
                                <small class="d-block">Quy trình hoàn chỉnh</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a asp-controller="Booking" asp-action="Search" class="btn btn-info w-100 py-3">
                                <i class="bi bi-search me-2"></i>
                                <div>Tìm kiếm vé</div>
                                <small class="d-block">Tìm kiếm nâng cao</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a asp-controller="Admin" asp-action="Index" class="btn btn-warning w-100 py-3">
                                <i class="bi bi-gear me-2"></i>
                                <div>Quản trị</div>
                                <small class="d-block">Dashboard admin</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Flow -->
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-diagram-3 me-2"></i>Quy trình đặt vé hoàn chỉnh
                    </h5>
                </div>
                <div class="card-body">
                    <div class="demo-flow">
                        <div class="flow-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h6 class="fw-bold">Tìm kiếm chuyến xe</h6>
                                <p class="text-muted mb-2">Tìm kiếm với bộ lọc nâng cao</p>
                                <a asp-controller="Booking" asp-action="Search" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-search me-1"></i>Tìm kiếm
                                </a>
                            </div>
                        </div>

                        <div class="flow-arrow">
                            <i class="bi bi-arrow-right"></i>
                        </div>

                        <div class="flow-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h6 class="fw-bold">Chọn ghế ngồi</h6>
                                <p class="text-muted mb-2">Sơ đồ ghế trực quan theo loại xe</p>
                                <span class="badge bg-success">Tự động</span>
                            </div>
                        </div>

                        <div class="flow-arrow">
                            <i class="bi bi-arrow-right"></i>
                        </div>

                        <div class="flow-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h6 class="fw-bold">Thông tin hành khách</h6>
                                <p class="text-muted mb-2">Nhập thông tin hoặc chọn từ danh bạ</p>
                                <span class="badge bg-info">Tự động điền</span>
                            </div>
                        </div>

                        <div class="flow-arrow">
                            <i class="bi bi-arrow-right"></i>
                        </div>

                        <div class="flow-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h6 class="fw-bold">Chọn thanh toán</h6>
                                <p class="text-muted mb-2">VNPay, MoMo, ZaloPay, Tại quầy</p>
                                <span class="badge bg-warning">Đa dạng</span>
                            </div>
                        </div>

                        <div class="flow-arrow">
                            <i class="bi bi-arrow-right"></i>
                        </div>

                        <div class="flow-step">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <h6 class="fw-bold">Xác nhận</h6>
                                <p class="text-muted mb-2">Email/SMS + QR Code</p>
                                <span class="badge bg-success">Hoàn tất</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sample Data -->
    @if (Model.SampleTrips.Any())
    {
        <div class="row g-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-event me-2"></i>Chuyến xe mẫu (có thể đặt vé)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            @foreach (var trip in Model.SampleTrips)
                            {
                                <div class="col-md-6 col-lg-4">
                                    <div class="card border h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="fw-bold text-primary">@trip.DiemDi → @trip.DiemDen</h6>
                                                <span class="badge bg-success">@trip.Xe?.LoaiXe</span>
                                            </div>
                                            <p class="text-muted mb-2">
                                                <i class="bi bi-calendar me-1"></i>
                                                @trip.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")
                                            </p>
                                            <p class="text-muted mb-2">
                                                <i class="bi bi-truck me-1"></i>
                                                @trip.Xe?.BienSo
                                            </p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <strong class="text-success">@string.Format("{0:N0}", trip.Gia) VNĐ</strong>
                                                <a asp-controller="Booking" asp-action="SelectTrip" asp-route-id="@trip.ChuyenXeId" 
                                                   class="btn btn-sm btn-primary">
                                                    <i class="bi bi-ticket me-1"></i>Đặt vé
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-info">
            <h5><i class="bi bi-info-circle me-2"></i>Chưa có dữ liệu</h5>
            <p class="mb-0">Vui lòng tạo dữ liệu demo để test quy trình đặt vé.</p>
        </div>
    }
</div>

<style>
    .demo-flow {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .flow-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        max-width: 150px;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #007bff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .step-content h6 {
        margin-bottom: 0.5rem;
    }

    .flow-arrow {
        font-size: 1.5rem;
        color: #6c757d;
        margin: 0 1rem;
    }

    @@media (max-width: 768px) {
        .demo-flow {
            flex-direction: column;
        }
        
        .flow-arrow {
            transform: rotate(90deg);
            margin: 1rem 0;
        }
    }

    .card {
        border-radius: 15px;
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .btn {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }
</style>

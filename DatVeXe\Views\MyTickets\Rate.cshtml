@model DatVeXe.Models.RatingViewModel
@{
    ViewData["Title"] = "Đánh giá chuyến đi - " + Model.MaVe;
}

<div class="container py-4">
    <!-- Header -->
    <div class="text-center mb-4">
        <h1 class="display-6 fw-bold text-warning">
            <i class="bi bi-star-fill me-3"></i><PERSON><PERSON><PERSON> gi<PERSON> chuyến đi
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
                <li class="breadcrumb-item"><a asp-action="Index">Vé của tôi</a></li>
                <li class="breadcrumb-item"><a asp-action="Details" asp-route-id="@Model.VeId">@Model.MaVe</a></li>
                <li class="breadcrumb-item active">Đánh gi<PERSON></li>
            </ol>
        </nav>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Trip Information -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>Thông tin chuyến đi
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-ticket-perforated text-primary me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold fs-5">@Model.MaVe</div>
                                    <small class="text-muted">Mã vé</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-calendar-event text-info me-3 fs-4"></i>
                                <div>
                                    <div class="fw-bold">@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</div>
                                    <small class="text-muted">Ngày giờ khởi hành</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="bg-light rounded p-3">
                                <div class="row align-items-center">
                                    <div class="col-md-4 text-center">
                                        <div class="fw-bold text-primary">@Model.ChuyenXe.DiemDi</div>
                                        <small class="text-muted">Điểm đi</small>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <i class="bi bi-arrow-right-circle text-primary fs-3"></i>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <div class="fw-bold text-primary">@Model.ChuyenXe.DiemDen</div>
                                        <small class="text-muted">Điểm đến</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rating Form -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="bi bi-star-fill me-2"></i>Đánh giá của bạn
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Rate" asp-route-id="@Model.VeId" method="post">
                        <!-- Overall Rating -->
                        <div class="mb-4">
                            <label class="form-label fw-semibold fs-5 mb-3">
                                <i class="bi bi-star me-2"></i>Đánh giá tổng thể <span class="text-danger">*</span>
                            </label>
                            <div class="text-center mb-3">
                                <div class="star-rating" data-rating="0" data-field="DiemDanhGia">
                                    <i class="bi bi-star star" data-value="1"></i>
                                    <i class="bi bi-star star" data-value="2"></i>
                                    <i class="bi bi-star star" data-value="3"></i>
                                    <i class="bi bi-star star" data-value="4"></i>
                                    <i class="bi bi-star star" data-value="5"></i>
                                </div>
                                <div class="rating-text mt-2 fw-bold text-warning fs-5"></div>
                            </div>
                            <input asp-for="DiemDanhGia" type="hidden" />
                            <span asp-validation-for="DiemDanhGia" class="text-danger"></span>
                        </div>

                        <!-- Detailed Ratings -->
                        <div class="row g-4 mb-4">
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-truck me-2"></i>Chất lượng xe
                                </label>
                                <div class="star-rating" data-rating="0" data-field="ChatLuongXe">
                                    <i class="bi bi-star star" data-value="1"></i>
                                    <i class="bi bi-star star" data-value="2"></i>
                                    <i class="bi bi-star star" data-value="3"></i>
                                    <i class="bi bi-star star" data-value="4"></i>
                                    <i class="bi bi-star star" data-value="5"></i>
                                </div>
                                <input asp-for="ChatLuongXe" type="hidden" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-person-check me-2"></i>Thái độ tài xế
                                </label>
                                <div class="star-rating" data-rating="0" data-field="ThaiDoTaiXe">
                                    <i class="bi bi-star star" data-value="1"></i>
                                    <i class="bi bi-star star" data-value="2"></i>
                                    <i class="bi bi-star star" data-value="3"></i>
                                    <i class="bi bi-star star" data-value="4"></i>
                                    <i class="bi bi-star star" data-value="5"></i>
                                </div>
                                <input asp-for="ThaiDoTaiXe" type="hidden" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-clock me-2"></i>Đúng giờ
                                </label>
                                <div class="star-rating" data-rating="0" data-field="DungGio">
                                    <i class="bi bi-star star" data-value="1"></i>
                                    <i class="bi bi-star star" data-value="2"></i>
                                    <i class="bi bi-star star" data-value="3"></i>
                                    <i class="bi bi-star star" data-value="4"></i>
                                    <i class="bi bi-star star" data-value="5"></i>
                                </div>
                                <input asp-for="DungGio" type="hidden" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-currency-dollar me-2"></i>Giá cả hợp lý
                                </label>
                                <div class="star-rating" data-rating="0" data-field="GiaCa">
                                    <i class="bi bi-star star" data-value="1"></i>
                                    <i class="bi bi-star star" data-value="2"></i>
                                    <i class="bi bi-star star" data-value="3"></i>
                                    <i class="bi bi-star star" data-value="4"></i>
                                    <i class="bi bi-star star" data-value="5"></i>
                                </div>
                                <input asp-for="GiaCa" type="hidden" />
                            </div>
                        </div>

                        <!-- Comments -->
                        <div class="mb-4">
                            <label asp-for="NhanXet" class="form-label fw-semibold">
                                <i class="bi bi-chat-text me-2"></i>Nhận xét chi tiết
                            </label>
                            <textarea asp-for="NhanXet" class="form-control" rows="5" 
                                      placeholder="Chia sẻ trải nghiệm của bạn về chuyến đi này..."></textarea>
                            <div class="form-text">Nhận xét của bạn sẽ giúp chúng tôi cải thiện chất lượng dịch vụ</div>
                            <span asp-validation-for="NhanXet" class="text-danger"></span>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a asp-action="Details" asp-route-id="@Model.VeId" class="btn btn-outline-secondary btn-lg me-md-2">
                                <i class="bi bi-arrow-left me-2"></i>Quay lại
                            </a>
                            <button type="submit" class="btn btn-warning btn-lg" id="submitRating" disabled>
                                <i class="bi bi-star-fill me-2"></i>Gửi đánh giá
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            const ratingTexts = {
                1: 'Rất không hài lòng',
                2: 'Không hài lòng', 
                3: 'Bình thường',
                4: 'Hài lòng',
                5: 'Rất hài lòng'
            };

            // Star rating functionality
            $('.star-rating').each(function() {
                const $rating = $(this);
                const field = $rating.data('field');
                const $input = $(`input[name="${field}"]`);
                const $stars = $rating.find('.star');

                $stars.hover(function() {
                    const value = $(this).data('value');
                    highlightStars($rating, value);
                }, function() {
                    const currentRating = $rating.data('rating');
                    highlightStars($rating, currentRating);
                });

                $stars.click(function() {
                    const value = $(this).data('value');
                    $rating.data('rating', value);
                    $input.val(value);
                    highlightStars($rating, value);
                    
                    // Update rating text for overall rating
                    if (field === 'DiemDanhGia') {
                        $('.rating-text').text(ratingTexts[value]);
                    }
                    
                    checkFormValidity();
                });
            });

            function highlightStars($rating, value) {
                $rating.find('.star').each(function(index) {
                    if (index < value) {
                        $(this).removeClass('bi-star').addClass('bi-star-fill text-warning');
                    } else {
                        $(this).removeClass('bi-star-fill text-warning').addClass('bi-star');
                    }
                });
            }

            function checkFormValidity() {
                const overallRating = $('input[name="DiemDanhGia"]').val();
                const vehicleRating = $('input[name="ChatLuongXe"]').val();
                const driverRating = $('input[name="ThaiDoTaiXe"]').val();
                const punctualityRating = $('input[name="DungGio"]').val();
                const priceRating = $('input[name="GiaCa"]').val();

                const isValid = overallRating > 0 && vehicleRating > 0 && 
                               driverRating > 0 && punctualityRating > 0 && priceRating > 0;
                
                $('#submitRating').prop('disabled', !isValid);
            }

            // Form submission confirmation
            $('#submitRating').click(function(e) {
                if (!confirm('Bạn có chắc chắn muốn gửi đánh giá này?')) {
                    e.preventDefault();
                }
            });
        });
    </script>
}

<style>
    .card {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        padding: 0.75rem 2rem;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
    }
    
    .form-control:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
    }
    
    .star-rating {
        font-size: 2rem;
        cursor: pointer;
        user-select: none;
    }
    
    .star-rating .star {
        transition: all 0.2s ease;
        margin: 0 2px;
    }
    
    .star-rating .star:hover {
        transform: scale(1.1);
    }
    
    .rating-text {
        min-height: 1.5rem;
    }
</style>

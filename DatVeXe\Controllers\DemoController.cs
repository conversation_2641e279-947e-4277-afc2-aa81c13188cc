using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;

namespace DatVeXe.Controllers
{
    public class DemoController : Controller
    {
        private readonly DatVeXeContext _context;
        private readonly ILogger<DemoController> _logger;

        public DemoController(DatVeXeContext context, ILogger<DemoController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: Demo - Trang demo hệ thống
        public async Task<IActionResult> Index()
        {
            var demoData = new DemoViewModel
            {
                TotalVehicles = await _context.Xes.CountAsync(),
                TotalRoutes = await _context.TuyenDuongs.CountAsync(),
                TotalTrips = await _context.ChuyenXes.CountAsync(),
                TotalSeats = await _context.ChoNgois.CountAsync(),
                
                SampleVehicles = await _context.Xes.Take(3).ToListAsync(),
                SampleRoutes = await _context.TuyenDuongs.Take(3).ToListAsync(),
                SampleTrips = await _context.ChuyenXes
                    .Include(c => c.Xe)
                    .Include(c => c.<PERSON>yenDuong)
                    .Where(c => c.NgayKhoiHanh > DateTime.Now)
                    .Take(5)
                    .ToListAsync()
            };

            return View(demoData);
        }

        // GET: Demo/CreateFullData - Tạo dữ liệu đầy đủ cho demo
        public async Task<IActionResult> CreateFullData()
        {
            try
            {
                // Xóa dữ liệu cũ
                await ClearAllData();

                // Tạo dữ liệu mới
                await CreateComprehensiveData();

                TempData["Success"] = "Đã tạo dữ liệu demo đầy đủ thành công! Bạn có thể test toàn bộ quy trình đặt vé.";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi tạo dữ liệu demo");
                TempData["Error"] = "Có lỗi xảy ra: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        private async Task ClearAllData()
        {
            // Xóa theo thứ tự để tránh foreign key constraint
            _context.DanhGiaChuyenDis.RemoveRange(_context.DanhGiaChuyenDis);
            _context.ThanhToans.RemoveRange(_context.ThanhToans);
            _context.Ves.RemoveRange(_context.Ves);
            _context.ChoNgois.RemoveRange(_context.ChoNgois);
            _context.ChuyenXes.RemoveRange(_context.ChuyenXes);
            _context.KhuyenMais.RemoveRange(_context.KhuyenMais);
            _context.TuyenDuongs.RemoveRange(_context.TuyenDuongs);
            _context.Xes.RemoveRange(_context.Xes);
            
            await _context.SaveChangesAsync();
        }

        private async Task CreateComprehensiveData()
        {
            // 1. Tạo xe
            var vehicles = new List<Xe>
            {
                new Xe { BienSo = "29A-12345", LoaiXe = "Limousine", SoGhe = 22, HangXe = "Hyundai", MauXe = "Trắng", NamSanXuat = 2023, TrangThaiHoatDong = true },
                new Xe { BienSo = "30B-67890", LoaiXe = "Giường nằm", SoGhe = 40, HangXe = "Thaco", MauXe = "Xanh", NamSanXuat = 2022, TrangThaiHoatDong = true },
                new Xe { BienSo = "31C-11111", LoaiXe = "Ghế ngồi", SoGhe = 45, HangXe = "Isuzu", MauXe = "Đỏ", NamSanXuat = 2021, TrangThaiHoatDong = true },
                new Xe { BienSo = "51D-22222", LoaiXe = "VIP", SoGhe = 28, HangXe = "Mercedes", MauXe = "Đen", NamSanXuat = 2024, TrangThaiHoatDong = true },
                new Xe { BienSo = "43E-33333", LoaiXe = "Limousine", SoGhe = 24, HangXe = "Hyundai", MauXe = "Bạc", NamSanXuat = 2023, TrangThaiHoatDong = true }
            };

            _context.Xes.AddRange(vehicles);
            await _context.SaveChangesAsync();

            // 2. Tạo ghế cho từng xe
            foreach (var vehicle in vehicles)
            {
                var seats = new List<ChoNgoi>();
                for (int i = 1; i <= vehicle.SoGhe; i++)
                {
                    seats.Add(new ChoNgoi
                    {
                        XeId = vehicle.XeId,
                        SoGhe = i.ToString().PadLeft(2, '0'),
                        LoaiGhe = vehicle.LoaiXe,
                        TrangThai = TrangThaiChoNgoi.Trong,
                        Hang = (i - 1) / 4 + 1,
                        Cot = (i - 1) % 4 + 1,
                        TrangThaiHoatDong = true
                    });
                }
                _context.ChoNgois.AddRange(seats);
            }

            // 3. Tạo tuyến đường
            var routes = new List<TuyenDuong>
            {
                new TuyenDuong { DiemDi = "Hà Nội", DiemDen = "Hồ Chí Minh", KhoangCach = 1700, ThoiGianDuKien = TimeSpan.FromHours(24), TrangThaiHoatDong = true },
                new TuyenDuong { DiemDi = "Hà Nội", DiemDen = "Đà Nẵng", KhoangCach = 800, ThoiGianDuKien = TimeSpan.FromHours(12), TrangThaiHoatDong = true },
                new TuyenDuong { DiemDi = "Hồ Chí Minh", DiemDen = "Đà Lạt", KhoangCach = 300, ThoiGianDuKien = TimeSpan.FromHours(6), TrangThaiHoatDong = true },
                new TuyenDuong { DiemDi = "Hà Nội", DiemDen = "Hải Phòng", KhoangCach = 120, ThoiGianDuKien = TimeSpan.FromHours(2), TrangThaiHoatDong = true },
                new TuyenDuong { DiemDi = "Hồ Chí Minh", DiemDen = "Cần Thơ", KhoangCach = 170, ThoiGianDuKien = TimeSpan.FromHours(3), TrangThaiHoatDong = true },
                new TuyenDuong { DiemDi = "Đà Nẵng", DiemDen = "Hội An", KhoangCach = 30, ThoiGianDuKien = TimeSpan.FromMinutes(45), TrangThaiHoatDong = true }
            };

            _context.TuyenDuongs.AddRange(routes);
            await _context.SaveChangesAsync();

            // 4. Tạo chuyến xe
            var trips = new List<ChuyenXe>();
            var random = new Random();

            for (int i = 0; i < 20; i++)
            {
                var vehicle = vehicles[random.Next(vehicles.Count)];
                var route = routes[random.Next(routes.Count)];
                var departureDate = DateTime.Now.AddDays(random.Next(1, 30)).AddHours(random.Next(6, 22));

                var basePrice = route.KhoangCach * 500; // 500 VND per km
                var vehicleMultiplier = vehicle.LoaiXe switch
                {
                    "VIP" => 2.0m,
                    "Limousine" => 1.5m,
                    "Giường nằm" => 1.2m,
                    _ => 1.0m
                };

                trips.Add(new ChuyenXe
                {
                    XeId = vehicle.XeId,
                    TuyenDuongId = route.TuyenDuongId,
                    NgayKhoiHanh = departureDate,
                    Gia = (int)(basePrice * vehicleMultiplier),
                    DiemDi = route.DiemDi,
                    DiemDen = route.DiemDen,
                    TrangThaiChuyenXe = TrangThaiChuyenXe.HoatDong
                });
            }

            _context.ChuyenXes.AddRange(trips);
            await _context.SaveChangesAsync();

            // 5. Tạo khuyến mãi
            var promotions = new List<KhuyenMai>
            {
                new KhuyenMai
                {
                    TenKhuyenMai = "Giảm 25% mùa hè",
                    MaKhuyenMai = "SUMMER25",
                    MoTa = "Giảm 25% cho tất cả chuyến xe trong mùa hè",
                    PhanTramGiam = 25,
                    SoTienGiamToiDa = 200000,
                    GiaTriDonHangToiThieu = 300000,
                    NgayBatDau = DateTime.Now,
                    NgayKetThuc = DateTime.Now.AddDays(60),
                    TrangThaiHoatDong = true,
                    ApDungChoKhachHangMoi = false,
                    NgayTao = DateTime.Now
                },
                new KhuyenMai
                {
                    TenKhuyenMai = "Khách hàng mới 15%",
                    MaKhuyenMai = "NEWBIE15",
                    MoTa = "Giảm 15% cho khách hàng đăng ký lần đầu",
                    PhanTramGiam = 15,
                    SoTienGiamToiDa = 100000,
                    GiaTriDonHangToiThieu = 200000,
                    NgayBatDau = DateTime.Now,
                    NgayKetThuc = DateTime.Now.AddDays(90),
                    TrangThaiHoatDong = true,
                    ApDungChoKhachHangMoi = true,
                    NgayTao = DateTime.Now
                },
                new KhuyenMai
                {
                    TenKhuyenMai = "Cuối tuần 10%",
                    MaKhuyenMai = "WEEKEND10",
                    MoTa = "Giảm 10% cho chuyến xe cuối tuần",
                    PhanTramGiam = 10,
                    SoTienGiamToiDa = 50000,
                    GiaTriDonHangToiThieu = 150000,
                    NgayBatDau = DateTime.Now,
                    NgayKetThuc = DateTime.Now.AddDays(30),
                    TrangThaiHoatDong = true,
                    ApDungChoKhachHangMoi = false,
                    NgayTao = DateTime.Now
                },
                new KhuyenMai
                {
                    TenKhuyenMai = "Flash Sale 30%",
                    MaKhuyenMai = "FLASH30",
                    MoTa = "Flash sale giảm 30% trong thời gian có hạn",
                    PhanTramGiam = 30,
                    SoTienGiamToiDa = 300000,
                    GiaTriDonHangToiThieu = 500000,
                    NgayBatDau = DateTime.Now,
                    NgayKetThuc = DateTime.Now.AddDays(7),
                    TrangThaiHoatDong = true,
                    ApDungChoKhachHangMoi = false,
                    NgayTao = DateTime.Now
                }
            };

            _context.KhuyenMais.AddRange(promotions);
            await _context.SaveChangesAsync();
        }

        // GET: Demo/TestBooking - Test quy trình đặt vé
        public async Task<IActionResult> TestBooking()
        {
            // Lấy chuyến xe đầu tiên để test
            var trip = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .FirstOrDefaultAsync();

            if (trip == null)
            {
                TempData["Error"] = "Chưa có dữ liệu chuyến xe. Vui lòng tạo dữ liệu demo trước.";
                return RedirectToAction("Index");
            }

            // Chuyển hướng đến trang đặt vé với chuyến xe này
            return RedirectToAction("SelectTrip", "Booking", new { id = trip.ChuyenXeId });
        }
    }

    // ViewModel cho Demo
    public class DemoViewModel
    {
        public int TotalVehicles { get; set; }
        public int TotalRoutes { get; set; }
        public int TotalTrips { get; set; }
        public int TotalSeats { get; set; }

        public List<Xe> SampleVehicles { get; set; } = new List<Xe>();
        public List<TuyenDuong> SampleRoutes { get; set; } = new List<TuyenDuong>();
        public List<ChuyenXe> SampleTrips { get; set; } = new List<ChuyenXe>();
    }
}

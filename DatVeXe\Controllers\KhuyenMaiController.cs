using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;

namespace DatVeXe.Controllers
{
    public class KhuyenMaiController : Controller
    {
        private readonly DatVeXeContext _context;

        public KhuyenMaiController(DatVeXeContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var now = DateTime.Now;
            var khuyenMais = await _context.KhuyenMais
                .Where(k => k.TrangThaiHoatDong &&
                           k.Ngay<PERSON>at<PERSON>au <= now &&
                           k.NgayKetThuc >= now &&
                           (k.SoLuong == null || k.SoLuongDaSuDung < k.SoLuong))
                .OrderByDescending(k => k.PhanTramG<PERSON>)
                .ThenByDescending(k => k.<PERSON><PERSON>)
                .ToListAsync();

            return View(khuyenMais);
        }
    }
}

@model DatVeXe.Models.BookingConfirmationViewModel
@{
    ViewData["Title"] = "Xác nhận đặt vé";
}

<div class="container py-4">
    <!-- Success Header -->
    <div class="text-center mb-5">
        <div class="success-icon mb-3">
            <i class="bi bi-check-circle-fill text-success"></i>
        </div>
        <h1 class="display-4 fw-bold text-success mb-3">Đặt vé thành công!</h1>
        <p class="lead text-muted">Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi</p>

        @if (TempData["Success"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                <i class="bi bi-check-circle me-2"></i>
                @TempData["Success"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Ticket Information -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-success text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-ticket-perforated me-2"></i>Thông tin vé xe
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6">
                            <div class="ticket-info">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-hash text-primary me-1"></i>Mã vé
                                    </div>
                                    <div class="info-value ticket-code">@Model.MaVe</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-person text-warning me-1"></i>Hành khách
                                    </div>
                                    <div class="info-value">@Model.TenKhach</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-telephone text-info me-1"></i>Số điện thoại
                                    </div>
                                    <div class="info-value">@Model.SoDienThoai</div>
                                </div>

                                @if (!string.IsNullOrEmpty(Model.Email))
                                {
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-envelope text-secondary me-1"></i>Email
                                        </div>
                                        <div class="info-value">@Model.Email</div>
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6">
                            <div class="trip-info">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-geo-alt text-success me-1"></i>Tuyến đường
                                    </div>
                                    <div class="info-value route-display">
                                        @Model.ChuyenXe.DiemDiDisplay
                                        <i class="bi bi-arrow-right mx-2"></i>
                                        @Model.ChuyenXe.DiemDenDisplay
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-calendar-event text-primary me-1"></i>Thời gian khởi hành
                                    </div>
                                    <div class="info-value">
                                        <div class="fw-bold">@Model.ChuyenXe.NgayKhoiHanh.ToString("dddd, dd/MM/yyyy")</div>
                                        <div class="text-success fs-5">@Model.ChuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-truck text-warning me-1"></i>Phương tiện
                                    </div>
                                    <div class="info-value">
                                        @Model.ChuyenXe.Xe?.LoaiXe
                                        <small class="text-muted d-block">@Model.ChuyenXe.Xe?.BienSo</small>
                                    </div>
                                </div>

                                @if (Model.ChoNgoi != null)
                                {
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-square-fill text-success me-1"></i>Ghế ngồi
                                        </div>
                                        <div class="info-value">
                                            <span class="badge bg-success fs-6">Ghế @Model.ChoNgoi.SoGhe</span>
                                            <small class="text-muted d-block">@Model.ChoNgoi.LoaiGhe</small>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Payment Information -->
                    <div class="payment-summary">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-2">
                                    <i class="bi bi-credit-card text-primary me-1"></i>Thông tin thanh toán
                                </h6>
                                @if (Model.ThanhToan != null)
                                {
                                    <div class="payment-details">
                                        <div class="payment-method">
                                            <span class="badge bg-info">@Model.ThanhToan.PhuongThuc.GetDisplayName()</span>
                                        </div>
                                        <div class="payment-status mt-2">
                                            @if (Model.ThanhToan.TrangThai == TrangThaiThanhToan.ThanhCong)
                                            {
                                                <span class="badge bg-success">
                                                    <i class="bi bi-check-circle me-1"></i>Đã thanh toán
                                                </span>
                                            }
                                            else if (Model.ThanhToan.TrangThai == TrangThaiThanhToan.ChoThanhToan)
                                            {
                                                <span class="badge bg-warning">
                                                    <i class="bi bi-clock me-1"></i>Chờ thanh toán
                                                </span>
                                            }
                                        </div>
                                    </div>
                                }
                            </div>
                            <div class="col-md-6 text-md-end">
                                <div class="total-amount">
                                    <div class="amount-label">Tổng tiền</div>
                                    <div class="amount-value">@string.Format("{0:N0}", Model.GiaVe) VNĐ</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Status -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-bell me-2"></i>Trạng thái thông báo
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="notification-item">
                                <div class="d-flex align-items-center">
                                    @if (Model.EmailSent)
                                    {
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        <span class="text-success">Email đã được gửi</span>
                                    }
                                    else
                                    {
                                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                        <span class="text-danger">Không thể gửi email</span>
                                    }
                                </div>
                                @if (!string.IsNullOrEmpty(Model.Email))
                                {
                                    <small class="text-muted">@Model.Email</small>
                                }
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="notification-item">
                                <div class="d-flex align-items-center">
                                    @if (Model.SMSSent)
                                    {
                                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                                        <span class="text-success">SMS đã được gửi</span>
                                    }
                                    else
                                    {
                                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                        <span class="text-danger">Không thể gửi SMS</span>
                                    }
                                </div>
                                <small class="text-muted">@Model.SoDienThoai</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Important Notes -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>Lưu ý quan trọng
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="bi bi-check text-success me-2"></i>
                            Vui lòng mang theo mã vé <strong>@Model.MaVe</strong> khi lên xe
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check text-success me-2"></i>
                            Có mặt tại bến xe trước giờ khởi hành ít nhất 15 phút
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check text-success me-2"></i>
                            Mang theo giấy tờ tùy thân để đối chiếu thông tin
                        </li>
                        @if (Model.ThanhToan?.TrangThai == TrangThaiThanhToan.ChoThanhToan)
                        {
                            <li class="mb-2">
                                <i class="bi bi-exclamation-circle text-warning me-2"></i>
                                Vui lòng hoàn tất thanh toán tại quầy trước khi lên xe
                            </li>
                        }
                    </ul>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="@Url.Action("Search")" class="btn btn-primary btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                    </a>
                    <a href="@Url.Action("Index", "Home")" class="btn btn-outline-secondary btn-lg">
                        <i class="bi bi-house me-2"></i>Về trang chủ
                    </a>
                    @if (Context.Session.GetInt32("UserId") != null)
                    {

                    }
                    <button type="button" class="btn btn-outline-success btn-lg" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>In vé
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .success-icon i {
        font-size: 4rem;
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .ticket-info .info-item,
    .trip-info .info-item {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f8f9fa;
    }

    .info-label {
        font-weight: 500;
        color: #6c757d;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .info-value {
        font-weight: 600;
        color: #495057;
        font-size: 1rem;
    }

    .ticket-code {
        font-family: 'Courier New', monospace;
        font-size: 1.25rem;
        color: #007bff;
        background: #f8f9ff;
        padding: 0.5rem;
        border-radius: 5px;
        border: 2px dashed #007bff;
    }

    .route-display {
        font-size: 1.1rem;
        color: #28a745;
    }

    .payment-summary {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
    }

    .amount-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .amount-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
    }

    .notification-item {
        padding: 0.75rem 0;
    }

    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    @@media print {
        .btn, .card-header {
            display: none !important;
        }

        .card {
            border: 1px solid #000 !important;
            box-shadow: none !important;
        }
    }
</style>

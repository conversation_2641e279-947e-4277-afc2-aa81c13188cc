@model DatVeXe.Models.MyTicketsViewModel
@{
    ViewData["Title"] = "Vé của tôi";
}

<div class="container py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-5 fw-bold text-primary">
            <i class="bi bi-ticket-perforated me-3"></i>Vé của tôi
        </h1>
        <a asp-controller="Booking" asp-action="Search" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-6 text-primary mb-2">
                        <i class="bi bi-ticket-detailed"></i>
                    </div>
                    <h5 class="card-title">Tổng số vé</h5>
                    <h3 class="text-primary fw-bold">@Model.TongSoVe</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-6 text-warning mb-2">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <h5 class="card-title">Sắp tới</h5>
                    <h3 class="text-warning fw-bold">@Model.SoVeSapToi</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-6 text-success mb-2">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <h5 class="card-title">Đã hoàn thành</h5>
                    <h3 class="text-success fw-bold">@Model.VeDaHoanThanh.Count</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-6 text-info mb-2">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    <h5 class="card-title">Tổng chi tiêu</h5>
                    <h3 class="text-info fw-bold">@string.Format("{0:N0}", Model.TongChiTieu) VNĐ</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <ul class="nav nav-pills nav-fill mb-4" id="ticketTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="active-tab" data-bs-toggle="pill" data-bs-target="#active" type="button" role="tab">
                <i class="bi bi-ticket-perforated me-2"></i>Vé đã đặt (@Model.VeDaDat.Count)
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="upcoming-tab" data-bs-toggle="pill" data-bs-target="#upcoming" type="button" role="tab">
                <i class="bi bi-clock me-2"></i>Sắp tới (@Model.VeSapToi.Count)
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="completed-tab" data-bs-toggle="pill" data-bs-target="#completed" type="button" role="tab">
                <i class="bi bi-check-circle me-2"></i>Đã hoàn thành (@Model.VeDaHoanThanh.Count)
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="cancelled-tab" data-bs-toggle="pill" data-bs-target="#cancelled" type="button" role="tab">
                <i class="bi bi-x-circle me-2"></i>Đã hủy (@Model.VeDaHuy.Count)
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="ticketTabsContent">
        <!-- Vé đã đặt -->
        <div class="tab-pane fade show active" id="active" role="tabpanel">
            @if (Model.VeDaDat.Any())
            {
                <div class="row g-4">
                    @foreach (var ve in Model.VeDaDat)
                    {
                        <div class="col-lg-6">
                            @await Html.PartialAsync("_TicketCard", ve)
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-ticket-perforated display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">Chưa có vé nào</h4>
                    <p class="text-muted">Hãy đặt vé đầu tiên của bạn!</p>
                    <a asp-controller="Booking" asp-action="Search" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Đặt vé ngay
                    </a>
                </div>
            }
        </div>

        <!-- Vé sắp tới -->
        <div class="tab-pane fade" id="upcoming" role="tabpanel">
            @if (Model.VeSapToi.Any())
            {
                <div class="row g-4">
                    @foreach (var ve in Model.VeSapToi)
                    {
                        <div class="col-lg-6">
                            @await Html.PartialAsync("_TicketCard", ve)
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-clock display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">Không có chuyến đi sắp tới</h4>
                </div>
            }
        </div>

        <!-- Vé đã hoàn thành -->
        <div class="tab-pane fade" id="completed" role="tabpanel">
            @if (Model.VeDaHoanThanh.Any())
            {
                <div class="row g-4">
                    @foreach (var ve in Model.VeDaHoanThanh)
                    {
                        <div class="col-lg-6">
                            @await Html.PartialAsync("_TicketCard", ve)
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-check-circle display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">Chưa có chuyến đi nào hoàn thành</h4>
                </div>
            }
        </div>

        <!-- Vé đã hủy -->
        <div class="tab-pane fade" id="cancelled" role="tabpanel">
            @if (Model.VeDaHuy.Any())
            {
                <div class="row g-4">
                    @foreach (var ve in Model.VeDaHuy)
                    {
                        <div class="col-lg-6">
                            @await Html.PartialAsync("_TicketCard", ve)
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-x-circle display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">Chưa có vé nào bị hủy</h4>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Auto-refresh every 30 seconds for upcoming trips
            setInterval(function() {
                if ($('#upcoming-tab').hasClass('active')) {
                    // Refresh upcoming trips data
                    location.reload();
                }
            }, 30000);
        });
    </script>
}

<style>
    .nav-pills .nav-link {
        border-radius: 10px;
        margin: 0 5px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .nav-pills .nav-link:hover {
        transform: translateY(-2px);
    }
    
    .card {
        border-radius: 15px;
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
</style>
